using Robot.Domain.Common;
using Robot.Domain.ValueObjects;
using Robot.Shared.Constants;

namespace Robot.Domain.Entities;

/// <summary>
/// 赔率实体
/// </summary>
public class Odds : BaseEntity
{
    /// <summary>
    /// 私有构造函数，用于ORM
    /// </summary>
    private Odds() { }
    
    /// <summary>
    /// 创建赔率
    /// </summary>
    public Odds(
        string betType, 
        decimal oddsValue, 
        Money minBet = null, 
        Money maxBet = null, 
        Money totalLimit = null)
    {
        if (string.IsNullOrWhiteSpace(betType))
            throw new ArgumentException("投注类型不能为空", nameof(betType));
        if (oddsValue <= 0)
            throw new ArgumentException("赔率必须大于0", nameof(oddsValue));
            
        BetType = betType;
        OddsValue = oddsValue;
        MinBet = minBet ?? Money.Create(GameConstants.DefaultMinBet);
        MaxBet = maxBet ?? Money.Create(GameConstants.DefaultMaxBet);
        TotalLimit = totalLimit ?? Money.Create(GameConstants.DefaultMaxBet);
        IsActive = true;
    }
    
    /// <summary>
    /// 投注类型（主键）
    /// </summary>
    public string BetType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 赔率值
    /// </summary>
    public decimal OddsValue { get; private set; }
    
    /// <summary>
    /// 最低限投
    /// </summary>
    public Money MinBet { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 最高限投
    /// </summary>
    public Money MaxBet { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 总额限投
    /// </summary>
    public Money TotalLimit { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; private set; }
    
    /// <summary>
    /// 更新赔率
    /// </summary>
    public void UpdateOdds(decimal newOddsValue)
    {
        if (newOddsValue <= 0)
            throw new ArgumentException("赔率必须大于0", nameof(newOddsValue));
            
        if (OddsValue != newOddsValue)
        {
            OddsValue = newOddsValue;
            MarkAsUpdated();
            
            // 添加赔率更新事件
            AddDomainEvent(new OddsUpdatedEvent(BetType, OddsValue, DateTime.Now));
        }
    }
    
    /// <summary>
    /// 更新投注限额
    /// </summary>
    public void UpdateBetLimits(Money minBet, Money maxBet, Money totalLimit)
    {
        if (minBet.Value < 0 || maxBet.Value < 0 || totalLimit.Value < 0)
            throw new ArgumentException("投注限额不能为负数");
        if (minBet.Value > maxBet.Value)
            throw new ArgumentException("最低限投不能大于最高限投");
            
        MinBet = minBet;
        MaxBet = maxBet;
        TotalLimit = totalLimit;
        MarkAsUpdated();
    }
    
    /// <summary>
    /// 启用赔率
    /// </summary>
    public void Enable()
    {
        if (!IsActive)
        {
            IsActive = true;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 禁用赔率
    /// </summary>
    public void Disable()
    {
        if (IsActive)
        {
            IsActive = false;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 验证投注金额是否在限额范围内
    /// </summary>
    public bool IsValidBetAmount(Money betAmount)
    {
        return IsActive && 
               betAmount.Value >= MinBet.Value && 
               betAmount.Value <= MaxBet.Value;
    }
    
    /// <summary>
    /// 计算中奖金额
    /// </summary>
    public Money CalculateWinAmount(Money betAmount)
    {
        if (!IsValidBetAmount(betAmount))
            throw new ArgumentException("投注金额不在有效范围内", nameof(betAmount));
            
        return betAmount.Multiply(OddsValue);
    }
}

/// <summary>
/// 赔率更新事件
/// </summary>
public record OddsUpdatedEvent(string BetType, decimal NewOddsValue, DateTime OccurredOn) : IDomainEvent;
