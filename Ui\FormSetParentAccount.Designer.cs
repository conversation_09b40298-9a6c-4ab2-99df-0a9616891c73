﻿using System.ComponentModel;

namespace Robot.Ui;

partial class FormSetParentAccount
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }

        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormSetParentAccount));
        label1 = new System.Windows.Forms.Label();
        comboBox_Member = new System.Windows.Forms.ComboBox();
        button_Sure = new System.Windows.Forms.Button();
        label2 = new System.Windows.Forms.Label();
        textBox_返利比例 = new System.Windows.Forms.TextBox();
        SuspendLayout();
        // 
        // label1
        // 
        label1.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)134));
        label1.ForeColor = System.Drawing.Color.Red;
        label1.Location = new System.Drawing.Point(12, 26);
        label1.Name = "label1";
        label1.Size = new System.Drawing.Size(148, 23);
        label1.TabIndex = 0;
        label1.Text = "请选择他的拉手上级:";
        label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        // 
        // comboBox_Member
        // 
        comboBox_Member.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
        comboBox_Member.FormattingEnabled = true;
        comboBox_Member.Location = new System.Drawing.Point(166, 26);
        comboBox_Member.Name = "comboBox_Member";
        comboBox_Member.Size = new System.Drawing.Size(133, 25);
        comboBox_Member.TabIndex = 1;
        // 
        // button_Sure
        // 
        button_Sure.Font = new System.Drawing.Font("Microsoft YaHei UI", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)134));
        button_Sure.Location = new System.Drawing.Point(166, 128);
        button_Sure.Name = "button_Sure";
        button_Sure.Size = new System.Drawing.Size(133, 36);
        button_Sure.TabIndex = 2;
        button_Sure.Text = "确定";
        button_Sure.UseVisualStyleBackColor = true;
        button_Sure.Click += button_Sure_Click;
        // 
        // label2
        // 
        label2.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)134));
        label2.ForeColor = System.Drawing.Color.Red;
        label2.Location = new System.Drawing.Point(12, 73);
        label2.Name = "label2";
        label2.Size = new System.Drawing.Size(148, 23);
        label2.TabIndex = 3;
        label2.Text = "给拉手上级返利%:";
        label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        // 
        // textBox_返利比例
        // 
        textBox_返利比例.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
        textBox_返利比例.Location = new System.Drawing.Point(166, 75);
        textBox_返利比例.Name = "textBox_返利比例";
        textBox_返利比例.Size = new System.Drawing.Size(133, 23);
        textBox_返利比例.TabIndex = 4;
        textBox_返利比例.Text = "0";
        textBox_返利比例.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
        // 
        // FormSetParentAccount
        // 
        AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
        AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        ClientSize = new System.Drawing.Size(327, 183);
        Controls.Add(textBox_返利比例);
        Controls.Add(label2);
        Controls.Add(button_Sure);
        Controls.Add(comboBox_Member);
        Controls.Add(label1);
        Icon = ((System.Drawing.Icon)resources.GetObject("$this.Icon"));
        StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
        Text = "设置拉手上级";
        Load += FormSetParentAccount_Load;
        ResumeLayout(false);
        PerformLayout();
    }

    private System.Windows.Forms.TextBox textBox_返利比例;

    private System.Windows.Forms.Label label2;

    private System.Windows.Forms.Button button_Sure;

    private System.Windows.Forms.ComboBox comboBox_Member;

    private System.Windows.Forms.Label label1;

    #endregion
}