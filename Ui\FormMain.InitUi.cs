﻿using System.Globalization;
using ControlHelper;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;

namespace Robot.Ui;

public partial class FormMain
{
    /// <summary>
    /// 初始化UI
    /// </summary>
    private async Task InitUi()
    {
        try
        {
            await InitDataGridViewBetOrderDetailAsync();
            await InitDataGridViewAddMoneyAsync();
            await InitDataGridViewSubMoneyAsync();
            await InitDataGridViewPlatformLogAsync();

            // 设置表格只读
            dataGridView_MemberInfo.ReadOnly = true;
            dataGridView_BetOrderDetail.ReadOnly = true;
            dataGridView_AddMoney.ReadOnly = true;
            dataGridView_SubMoney.ReadOnly = true;
            dataGridView_PlatformLog.ReadOnly = true;
            dataGridView_BetOrderReport.ReadOnly = true;
            dataGridView_HuiZongReport.ReadOnly = true;
            dataGridView_AddMoneyReport.ReadOnly = true;
            dataGridView_SubMoneyReport.ReadOnly = true;
            dataGridView_FinanceReport.ReadOnly = true;
            dataGridView_RecMsg.ReadOnly = true;
            dataGridView_Log.ReadOnly = true;

            // 虚拟模式
            dataGridView_MemberInfo.VirtualMode = true;
            dataGridView_BetOrderReport.VirtualMode = true;
            dataGridView_HuiZongReport.VirtualMode = true;
            dataGridView_AddMoneyReport.VirtualMode = true;
            dataGridView_SubMoneyReport.VirtualMode = true;
            dataGridView_FinanceReport.VirtualMode = true;
            dataGridView_RecMsg.VirtualMode = true;
            dataGridView_Log.VirtualMode = true;

            // 表格相关设置
            MyControl.InitializeDataGridView(dataGridView_MemberInfo);
            MyControl.InitializeDataGridView(dataGridView_BetOrderDetail);
            MyControl.InitializeDataGridView(dataGridView_AddMoney);
            MyControl.InitializeDataGridView(dataGridView_SubMoney);
            MyControl.InitializeDataGridView(dataGridView_PlatformLog);
            MyControl.InitializeDataGridView(dataGridView_Odds);

            MyControl.InitializeDataGridView(dataGridView_BetOrderReport);
            MyControl.InitializeDataGridView(dataGridView_HuiZongReport);
            MyControl.InitializeDataGridView(dataGridView_AddMoneyReport);
            MyControl.InitializeDataGridView(dataGridView_SubMoneyReport);
            MyControl.InitializeDataGridView(dataGridView_FinanceReport);
            MyControl.InitializeDataGridView(dataGridView_RecMsg);
            MyControl.InitializeDataGridView(dataGridView_Log);
            MyControl.InitializeDataGridView(dataGridView_Odds);

            // 开启表格双缓冲
            MyControl.DoubleBuffered(dataGridView_MemberInfo);
            MyControl.DoubleBuffered(dataGridView_BetOrderDetail);
            MyControl.DoubleBuffered(dataGridView_AddMoney);
            MyControl.DoubleBuffered(dataGridView_SubMoney);
            MyControl.DoubleBuffered(dataGridView_PlatformLog);

            MyControl.DoubleBuffered(dataGridView_BetOrderReport);
            MyControl.DoubleBuffered(dataGridView_HuiZongReport);
            MyControl.DoubleBuffered(dataGridView_AddMoneyReport);
            MyControl.DoubleBuffered(dataGridView_SubMoneyReport);
            MyControl.DoubleBuffered(dataGridView_FinanceReport);
            MyControl.DoubleBuffered(dataGridView_RecMsg);
            MyControl.DoubleBuffered(dataGridView_Log);

            // 绑定事件
            dataGridView_AddMoney.CellClick += dataGridView_AddMoney_CellClick;
            dataGridView_SubMoney.CellClick += dataGridView_SubMoney_CellClick;
            dataGridView_PlatformLog.CellDoubleClick += dataGridView_PlatformLog_CellDoubleClick;
            dataGridView_Log.CellDoubleClick += dataGridView_Log_CellDoubleClick;
            dataGridView_Log.CellValueNeeded += dataGridView_Log_CellValueNeeded;

            dataGridView_MemberInfo.CellFormatting += dataGridView_MemberInfo_CellFormatting;
            dataGridView_AddMoney.CellFormatting += dataGridView_AddMoney_CellFormatting;
            dataGridView_SubMoney.CellFormatting += dataGridView_SubMoney_CellFormatting;
            dataGridView_PlatformLog.CellFormatting += dataGridView_PlatformLog_CellFormatting;
            dataGridView_BetOrderReport.CellFormatting += dataGridView_BetOrderReport_CellFormatting;
            dataGridView_HuiZongReport.CellFormatting += dataGridView_HuiZongReport_CellFormatting;

            // dataGridView_MemberInfo绑定鼠标点击事件
            dataGridView_MemberInfo.CellMouseDown += dataGridView_MemberInfo_CellMouseDown;
            button_SaveOdds.Click += button_SaveOdds_Click;
            comboBox_CloseTime.SelectedIndexChanged += comboBox_CloseTime_SelectedIndexChanged;
            checkBox_IsFeiDan.CheckedChanged += checkBox_IsFeiDan_CheckedChanged;
            checkBox_IsDuiChong.CheckedChanged += checkBox_IsDuiChong_CheckedChanged;
            checkBox_7Rows.CheckedChanged += checkBox_7Rows_CheckedChanged;
            checkBox_6Rows.CheckedChanged += checkBox_6Rows_CheckedChanged;
            button_SaveReturnCommissionPercent.Click += button_SaveReturnCommissionPercent_Click;
            button_CancelBetData.Click += button_CancelBetData_Click;
            button_OneKeyRebate.Click += button_OneKeyRebate_Click;
            checkBox_SaveMemberBaseInfo.CheckedChanged += checkBox_SaveMemberRemarkName_CheckedChanged;
            checkBox_SaveMemberBalance.CheckedChanged += checkBox_SaveMemberBalance_CheckedChanged;
            button_Clear.Click += button_Clear_Click;

            // 显示回水金额
            checkBox_ShowReturnCommissionDetail.Checked = Setting.Current.显示回水金额.Equals(1);
            checkBox_ShowReturnCommissionDetail.ForeColor = checkBox_ShowReturnCommissionDetail.Checked ? Color.Red : Color.Black;

            // 设置相关选项
            checkBox_IsDuiChong.Checked = Setting.Current.是否对冲吃单;
            checkBox_IsFeiDan.Checked = Setting.Current.是否开启飞单;
            checkBox_JiaRenAutoAddMoney.Checked = Setting.Current.假人自动上分;
            checkBox_AutoHuiShui.Checked = Setting.Current.自动回水;
            checkBox_7Rows.Checked = Setting.Current.SendImageRows7;
            checkBox_6Rows.Checked = Setting.Current.SendImageRows6;

            // 显示图片类型
            if (Setting.Current.ImgType.Equals(1))
            {
                radioButton_ImgType1.Checked = true;
            }
            else if (Setting.Current.ImgType.Equals(2))
            {
                radioButton_ImgType2.Checked = true;
            }

            // 初始化CloseTime下拉框时间
            for (int j = 60; j >= 0; j--)
            {
                comboBox_CloseTime.Items.Add(j);
            }

            // 获取值显示在UI上
            comboBox_CloseTime.Text = Setting.Current.封盘时间.ToString();
            textBox_ReturnCommissionPercent.Text = Setting.Current.回水比例.ToString(CultureInfo.InvariantCulture);

            // 默认选择
            if (Setting.Current.AppType.Equals(0))
            {
                comboBox_BetLottery.Items.Add("台湾宾果1");
                comboBox_BetLottery.Items.Add("台湾宾果2");
                comboBox_BetLottery.Items.Add("台湾宾果3");
            }
            else if (Setting.Current.AppType.Equals(1))
            {
                comboBox_BetLottery.Items.Add("168飞艇前3");
                comboBox_BetLottery.Items.Add("168飞艇中3");
                comboBox_BetLottery.Items.Add("168飞艇后3");
            }
            else if (Setting.Current.AppType.Equals(2))
            {
                comboBox_BetLottery.Items.Add("新168XL前");
                comboBox_BetLottery.Items.Add("新168XL中");
                comboBox_BetLottery.Items.Add("新168XL后");
            }

            comboBox_BetLottery.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "初始化UI", ex.ToString());
        }
    }

    /// <summary>
    /// 初始化BetOrderDetail表格
    /// </summary>
    private async Task InitDataGridViewBetOrderDetailAsync()
    {
        try
        {
            DataGridView dgv = dataGridView_BetOrderDetail;

            // 设置列宽
            dgv.Columns[^1]!.Width = 80;
            dgv.Columns[^2]!.Width = 80;
            dgv.Columns[^3]!.Width = 80;
            dgv.Columns[^4]!.Width = 200;
            dgv.Columns[^5]!.Width = 150;
            dgv.Columns[^6]!.Width = 100;

            // 设置对齐方式
            dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^6].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^7].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "初始化BetOrderDetail表格", ex.ToString());
        }
    }

    /// <summary>
    /// 初始化AddMoney表格
    /// </summary>
    private async Task InitDataGridViewAddMoneyAsync()
    {
        try
        {
            DataGridView dgv = dataGridView_AddMoney;

            // 设置列宽
            dgv.Columns[0]!.Width = 0;
            dgv.Columns[1]!.Width = 180;
            dgv.Columns[2]!.Width = 120;
            dgv.Columns[3]!.Width = 80;
            dgv.Columns[4]!.Width = 100;
            dgv.Columns[5]!.Width = 60;

            // 设置对齐方式
            dgv.Columns[1]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[2]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[3]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // 隐藏第1列
            dgv.Columns[0].Visible = false;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "初始化AddMoney表格", ex.ToString());
        }
    }

    /// <summary>
    /// 初始化SubMoney表格
    /// </summary>
    private async Task InitDataGridViewSubMoneyAsync()
    {
        try
        {
            DataGridView dgv = dataGridView_SubMoney;

            // 设置列宽
            dgv.Columns[0]!.Width = 0;
            dgv.Columns[1]!.Width = 180;
            dgv.Columns[2]!.Width = 120;
            dgv.Columns[3]!.Width = 80;
            dgv.Columns[4]!.Width = 100;
            dgv.Columns[5]!.Width = 60;

            // 设置对齐方式
            dgv.Columns[1]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[2]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[3]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // 隐藏第1列
            dgv.Columns[0].Visible = false;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "初始化SubMoney表格", ex.ToString());
        }
    }

    /// <summary>
    /// 初始化PlatformLog表格
    /// </summary>
    private async Task InitDataGridViewPlatformLogAsync()
    {
        try
        {
            DataGridView dgv = dataGridView_PlatformLog;

            // 设置第1列的日期时间格式
            dgv.Columns[1].DefaultCellStyle.Format = "HH:mm:ss";

            // 设置列宽
            dgv.Columns[0].Width = 0;
            dgv.Columns[1].Width = 80;
            dgv.Columns[2].Width = 120;
            dgv.Columns[3].Width = 280;

            // 设置列对齐方式
            dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // 隐藏第1列
            dgv.Columns[0].Visible = false;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "初始化SubMoney表格", ex.ToString());
        }
    }
}