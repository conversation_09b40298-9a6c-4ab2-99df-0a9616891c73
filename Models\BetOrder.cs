﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

[Table(Name = "BetOrder")]
public class BetOrder
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public DateTime Time { get; set; } = DateTime.Now;
    public string Account { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public EnumBetLottery BetLottery { get; set; }
    public string Con { get; set; } = string.Empty;
    public decimal Money { get; set; }
    public decimal Odds { get; set; }
    public int DrawResult { get; set; }
    public EnumBetWinLose WinLose { get; set; }
    public decimal 结算 { get; set; }
    public EnumBetOrderStatus BetOrderStatus { get; set; }
    public EnumBetOrderRebateStatus BetOrderRebateStatus { get; set; }
    public decimal 回水比例 { get; set; }
    public decimal 回水金额 { get; set; }
    public string FromMsgId { get; set; } = string.Empty;
    public EnumOrderType OrderType { get; set; } = EnumOrderType.真人订单;
}