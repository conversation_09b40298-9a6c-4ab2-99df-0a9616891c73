namespace Robot.Shared.Enums;

/// <summary>
/// 日志类型枚举
/// </summary>
public enum LogType
{
    /// <summary>
    /// 机器人日志
    /// </summary>
    Robot = 1,
    
    /// <summary>
    /// 平台日志
    /// </summary>
    Platform = 2
}

/// <summary>
/// 余额状态枚举
/// </summary>
public enum BalanceStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 1,
    
    /// <summary>
    /// 冻结
    /// </summary>
    Frozen = 2,
    
    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 3
}

/// <summary>
/// 平台类型枚举
/// </summary>
public enum PlatformType
{
    /// <summary>
    /// 本地平台
    /// </summary>
    Local = 1,
    
    /// <summary>
    /// 远程平台
    /// </summary>
    Remote = 2
}
