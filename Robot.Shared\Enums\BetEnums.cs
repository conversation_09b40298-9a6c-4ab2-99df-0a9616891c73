namespace Robot.Shared.Enums;

/// <summary>
/// 投注彩票类型枚举
/// </summary>
public enum BetLotteryType
{
    /// <summary>
    /// 台湾宾果1
    /// </summary>
    TaiwanBingo1 = 1,
    
    /// <summary>
    /// 台湾宾果2
    /// </summary>
    TaiwanBingo2 = 2,
    
    /// <summary>
    /// 台湾宾果3
    /// </summary>
    TaiwanBingo3 = 3,
    
    /// <summary>
    /// 一六八飞艇前3
    /// </summary>
    Flight168Front3 = 4,
    
    /// <summary>
    /// 一六八飞艇中3
    /// </summary>
    Flight168Middle3 = 5,
    
    /// <summary>
    /// 一六八飞艇后3
    /// </summary>
    Flight168Back3 = 6,
    
    /// <summary>
    /// 新一六八XL前
    /// </summary>
    New168XLFront = 7,
    
    /// <summary>
    /// 新一六八XL中
    /// </summary>
    New168XLMiddle = 8,
    
    /// <summary>
    /// 新一六八XL后
    /// </summary>
    New168XLBack = 9
}

/// <summary>
/// 投注结果枚举
/// </summary>
public enum BetWinLoseStatus
{
    /// <summary>
    /// 未开奖
    /// </summary>
    NotDrawn = 0,
    
    /// <summary>
    /// 中奖
    /// </summary>
    Win = 1,
    
    /// <summary>
    /// 未中奖
    /// </summary>
    Lose = 2,
    
    /// <summary>
    /// 和局
    /// </summary>
    Draw = 3
}

/// <summary>
/// 投注订单状态枚举
/// </summary>
public enum BetOrderStatus
{
    /// <summary>
    /// 已受理
    /// </summary>
    Accepted = 1,
    
    /// <summary>
    /// 已结算
    /// </summary>
    Settled = 2,
    
    /// <summary>
    /// 用户撤销
    /// </summary>
    UserCancelled = 3,
    
    /// <summary>
    /// 管理撤销
    /// </summary>
    AdminCancelled = 4
}

/// <summary>
/// 回水状态枚举
/// </summary>
public enum RebateStatus
{
    /// <summary>
    /// 未回水
    /// </summary>
    NotRebated = 0,
    
    /// <summary>
    /// 已回水
    /// </summary>
    Rebated = 1
}

/// <summary>
/// 订单类型枚举
/// </summary>
public enum OrderType
{
    /// <summary>
    /// 真人订单
    /// </summary>
    RealUser = 1,
    
    /// <summary>
    /// 假人订单
    /// </summary>
    Bot = 2
}

/// <summary>
/// 投注结果状态枚举
/// </summary>
public enum BetResultStatus
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 成功
    /// </summary>
    Success = 1,
    
    /// <summary>
    /// 失败
    /// </summary>
    Failed = 2
}
