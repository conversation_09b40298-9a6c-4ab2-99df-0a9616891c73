﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

[Table(Name = "Finance")]
public class Finance
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public DateTime 时间 { get; set; } = DateTime.Now;
    public string Account { get; set; } = string.Empty;
    public decimal 变动前 { get; set; }
    public decimal 变动值 { get; set; }
    public decimal 变动后 { get; set; }
    public string 凭据 { get; set; } = string.Empty;
    public string 对应信息 { get; set; } = string.Empty;
}