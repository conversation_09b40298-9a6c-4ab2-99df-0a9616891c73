using Robot.Domain.Common;

namespace Robot.Domain.ValueObjects;

/// <summary>
/// 金额值对象
/// </summary>
public class Money : ValueObject
{
    /// <summary>
    /// 私有构造函数
    /// </summary>
    private Money(decimal value)
    {
        Value = Math.Round(value, 2);
    }
    
    /// <summary>
    /// 金额值
    /// </summary>
    public decimal Value { get; private set; }
    
    /// <summary>
    /// 零金额
    /// </summary>
    public static Money Zero => new(0);
    
    /// <summary>
    /// 创建金额
    /// </summary>
    public static Money Create(decimal value)
    {
        if (value < 0)
            throw new ArgumentException("金额不能为负数", nameof(value));
            
        return new Money(value);
    }
    
    /// <summary>
    /// 加法运算
    /// </summary>
    public Money Add(Money other)
    {
        return new Money(Value + other.Value);
    }
    
    /// <summary>
    /// 减法运算
    /// </summary>
    public Money Subtract(Money other)
    {
        var result = Value - other.Value;
        if (result < 0)
            throw new InvalidOperationException("金额不能为负数");
            
        return new Money(result);
    }
    
    /// <summary>
    /// 乘法运算
    /// </summary>
    public Money Multiply(decimal multiplier)
    {
        if (multiplier < 0)
            throw new ArgumentException("乘数不能为负数", nameof(multiplier));
            
        return new Money(Value * multiplier);
    }
    
    /// <summary>
    /// 除法运算
    /// </summary>
    public Money Divide(decimal divisor)
    {
        if (divisor <= 0)
            throw new ArgumentException("除数必须大于0", nameof(divisor));
            
        return new Money(Value / divisor);
    }
    
    /// <summary>
    /// 获取相等性组件
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
    
    /// <summary>
    /// 字符串表示
    /// </summary>
    public override string ToString()
    {
        return Value.ToString("F2");
    }
    
    /// <summary>
    /// 隐式转换为decimal
    /// </summary>
    public static implicit operator decimal(Money money)
    {
        return money.Value;
    }
    
    /// <summary>
    /// 显式转换从decimal
    /// </summary>
    public static explicit operator Money(decimal value)
    {
        return Create(value);
    }
    
    /// <summary>
    /// 加法操作符
    /// </summary>
    public static Money operator +(Money left, Money right)
    {
        return left.Add(right);
    }
    
    /// <summary>
    /// 减法操作符
    /// </summary>
    public static Money operator -(Money left, Money right)
    {
        return left.Subtract(right);
    }
    
    /// <summary>
    /// 乘法操作符
    /// </summary>
    public static Money operator *(Money money, decimal multiplier)
    {
        return money.Multiply(multiplier);
    }
    
    /// <summary>
    /// 除法操作符
    /// </summary>
    public static Money operator /(Money money, decimal divisor)
    {
        return money.Divide(divisor);
    }
    
    /// <summary>
    /// 大于操作符
    /// </summary>
    public static bool operator >(Money left, Money right)
    {
        return left.Value > right.Value;
    }
    
    /// <summary>
    /// 小于操作符
    /// </summary>
    public static bool operator <(Money left, Money right)
    {
        return left.Value < right.Value;
    }
    
    /// <summary>
    /// 大于等于操作符
    /// </summary>
    public static bool operator >=(Money left, Money right)
    {
        return left.Value >= right.Value;
    }
    
    /// <summary>
    /// 小于等于操作符
    /// </summary>
    public static bool operator <=(Money left, Money right)
    {
        return left.Value <= right.Value;
    }
}
