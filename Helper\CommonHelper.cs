﻿using System.Collections.Concurrent;
using System.Reflection;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 公共帮助类 - 彩票机器人系统的核心工具类
///
/// 功能概述：
/// 1. 全局配置管理：管理应用程序的全局设置和状态
/// 2. 聊天平台适配：为不同聊天平台提供统一的表情符号接口
/// 3. 彩票游戏支持：定义彩票游戏的玩法内容和规则
/// 4. 系统状态跟踪：维护用户信息、投注状态等关键数据
/// 5. 跨平台兼容：处理不同聊天应用的表情符号编码差异
///
/// 设计模式：静态工具类，提供全局访问点
/// 线程安全：使用ConcurrentDictionary等线程安全集合
/// </summary>
public static class CommonHelper
{
    #region 系统核心配置

    /// <summary>
    /// 同步上下文 - 用于UI线程和后台线程之间的同步操作
    /// 主要用于确保UI更新操作在主线程中执行，避免跨线程操作异常
    /// </summary>
    public static SynchronizationContext Context { get; set; } = null!;

    /// <summary>
    /// 许可证服务器地址 - 用于软件授权验证和更新检查
    /// 该服务器提供：
    /// 1. 软件注册码验证
    /// 2. 版本更新检查
    /// 3. 授权到期时间验证
    /// </summary>
    public static string LicenseServer => "http://156.225.30.177";

    /// <summary>
    /// 服务启动标识键名 - 用于标识服务是否已启动的配置键
    /// 在系统参数字典中用作键名，值为布尔类型表示服务状态
    /// </summary>
    public static string IsStartService => "IsStartService";

    /// <summary>
    /// 应用程序版本号 - 从程序集中自动获取版本信息
    /// 用途：
    /// 1. 显示在软件界面标题栏
    /// 2. 版本更新检查对比
    /// 3. 日志记录和问题追踪
    /// </summary>
    public static string? AppVersion { get; set; } = Assembly.GetExecutingAssembly().GetName().Version?.ToString();

    /// <summary>
    /// 取消令牌源 - 用于优雅地取消异步操作和后台任务
    /// 当系统需要关闭时，通过此令牌通知所有异步任务停止执行
    /// 避免强制终止导致的数据不一致问题
    /// </summary>
    public static CancellationTokenSource Cts { get; set; } = new();

    #endregion

    #region 彩票游戏玩法配置

    /// <summary>
    /// 彩票游戏玩法内容列表 - 定义所有支持的投注玩法
    ///
    /// 玩法分类说明：
    ///
    /// 1. 正码玩法（1正-4正）：
    ///    - 预测开奖号码的第1、2、3、4位数字
    ///    - 例如：开奖1234，投注"1正"表示预测第1位是1
    ///
    /// 2. 番码玩法（1番-4番）：
    ///    - 与正码相对的玩法，预测不会出现的数字
    ///    - 例如：投注"1番"表示预测第1位不是1
    ///
    /// 3. 角码玩法（12角、23角、34角、14角）：
    ///    - 预测相邻或特定位置的数字组合
    ///    - 例如：12角表示第1位和第2位的组合
    ///
    /// 4. 念码玩法（1念2、1念3等）：
    ///    - 预测某位数字与其他位数字的关系
    ///    - 例如：1念2表示第1位数字与第2位数字的特定关系
    ///
    /// 5. 大小单双玩法：
    ///    - 单：开奖结果为奇数
    ///    - 双：开奖结果为偶数
    ///    - 大：开奖结果大于某个基准值
    ///    - 小：开奖结果小于某个基准值
    ///
    /// 6. 三门玩法（三门123、三门124等）：
    ///    - 预测三个数字的组合出现
    ///    - 例如：三门123表示1、2、3这三个数字的组合
    ///
    /// 注释掉的"无"系列玩法：
    /// - 表示某位数字不包含特定数字的玩法
    /// - 当前版本暂未启用，保留用于未来扩展
    /// </summary>
    public static List<string> PlayContentList { get; } =
    [
        "1正", // 第1位正码
        "2正", // 第2位正码
        "3正", // 第3位正码
        "4正", // 第4位正码
        "1番", // 第1位番码
        "2番", // 第2位番码
        "3番", // 第3位番码
        "4番", // 第4位番码
        "12角", // 第1、2位角码
        "23角", // 第2、3位角码
        "34角", // 第3、4位角码
        "14角", // 第1、4位角码
        "1念2", // 第1位念第2位
        "1念3", // 第1位念第3位
        "1念4", // 第1位念第4位
        "2念1", // 第2位念第1位
        "2念3", // 第2位念第3位
        "2念4", // 第2位念第4位
        "3念1", // 第3位念第1位
        "3念2", // 第3位念第2位
        "3念4", // 第3位念第4位
        "4念1", // 第4位念第1位
        "4念2", // 第4位念第2位
        "4念3", // 第4位念第3位
        "单", // 单数玩法
        "双", // 双数玩法
        // "大",       // 大数玩法
        // "小",       // 小数玩法
        // "三门123",  // 三门组合123
        // "三门124",  // 三门组合124
        // "三门134",  // 三门组合134
        // "三门234",  // 三门组合234
        // 以下为暂未启用的"无"系列玩法，保留用于未来扩展
        // "1无2",   // 第1位无第2位数字
        // "1无3",   // 第1位无第3位数字
        // "1无4",   // 第1位无第4位数字
        // "2无1",   // 第2位无第1位数字
        // "2无3",   // 第2位无第3位数字
        // "2无4",   // 第2位无第4位数字
        // "3无1",   // 第3位无第1位数字
        // "3无2",   // 第3位无第2位数字
        // "3无4",   // 第3位无第4位数字
        // "4无1",   // 第4位无第1位数字
        // "4无2",   // 第4位无第2位数字
        // "4无3"    // 第4位无第3位数字
    ];

    #endregion

    #region 系统运行状态配置

    /// <summary>
    /// 当前使用的聊天应用平台 - 系统支持多种聊天平台的核心配置
    ///
    /// 支持的平台包括：
    /// - 微信系列：微信360018、微信391016、微信391125、微信391216
    /// - QQ系列：QQ、MyQQ、GoQQ、LaQQ
    /// - 其他平台：一起聊吧、VoceChat
    ///
    /// 默认值：微信391125
    /// 作用：决定表情符号编码格式、消息发送接口、用户信息获取方式等
    /// </summary>
    public static EnumChatApp ChatApp { get; set; } = EnumChatApp.微信391125;

    /// <summary>
    /// 当前彩票游戏类型 - 决定系统运行的彩票游戏模式
    ///
    /// 支持的彩票类型：
    /// - 台湾宾果：每日203期，5分钟一期，07:00开始
    /// - 一六八飞艇：每日180期，5分钟一期，13:05开始
    /// - 新一六八XL：每日180期，5分钟一期，13:05开始
    ///
    /// 默认值：台湾宾果
    /// 作用：影响开奖时间、期号格式、投注规则等核心逻辑
    /// </summary>
    public static EnumLottery Lottery { get; set; } = EnumLottery.台湾宾果;

    /// <summary>
    /// 投注成功期号字典 - 记录每期投注是否成功的状态
    ///
    /// 键：期号（如：113000001）
    /// 值：是否投注成功（true/false）
    ///
    /// 用途：
    /// 1. 防止重复投注同一期号
    /// 2. 统计投注成功率
    /// 3. 异常情况下的状态恢复
    /// </summary>
    public static Dictionary<string, bool> BetSuccessIssueDic { get; set; } = new();

    /// <summary>
    /// 投注失败期号字典 - 记录投注失败的期号及原因
    ///
    /// 键：期号（如：113000001）
    /// 值：是否投注失败（true/false）
    ///
    /// 用途：
    /// 1. 记录投注失败的期号，避免重复尝试
    /// 2. 分析投注失败的模式和原因
    /// 3. 系统稳定性监控和优化
    /// </summary>
    public static Dictionary<string, bool> BetFailIssueDic { get; set; } = new();

    /// <summary>
    /// 服务启动时间 - 记录机器人服务开始运行的时间戳
    ///
    /// 默认值：1970年1月1日（Unix时间戳起点）
    /// 实际值：在服务启动时设置为当前时间
    ///
    /// 用途：
    /// 1. 计算服务运行时长
    /// 2. 性能统计和监控
    /// 3. 日志记录和问题排查
    /// </summary>
    public static DateTime StartServiceTime { get; set; } = new(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    #endregion

    #region 用户和会员信息管理

    /// <summary>
    /// 当前用户信息 - 存储登录用户的基本信息
    ///
    /// 包含字段：
    /// - UserName：用户名
    /// - Balance：账户余额
    ///
    /// 用途：
    /// 1. 用户身份验证和授权
    /// 2. 账户余额显示和管理
    /// 3. 用户操作权限控制
    /// </summary>
    public static UserInfo UserInfo { get; set; } = new();

    /// <summary>
    /// 服务参数字典 - 线程安全的系统参数存储容器
    ///
    /// 键：参数名称（字符串）
    /// 值：参数状态（布尔值）
    ///
    /// 特点：使用ConcurrentDictionary确保多线程环境下的安全访问
    ///
    /// 用途：
    /// 1. 存储系统运行时的动态配置
    /// 2. 服务状态标识（如IsStartService）
    /// 3. 功能开关控制
    /// </summary>
    public static ConcurrentDictionary<string, bool> ServiceParamDic { get; set; } = new();

    /// <summary>
    /// 当前会员信息 - 存储当前操作的会员详细信息
    ///
    /// 包含字段：
    /// - Account：会员账号
    /// - 昵称：会员昵称
    /// - 备注名：会员备注名
    /// - Balance：账户余额
    /// - 回水比例：返水比例
    /// - 是否假人：是否为机器人用户
    ///
    /// 用途：
    /// 1. 会员信息展示和管理
    /// 2. 投注权限和额度控制
    /// 3. 返水计算和结算
    /// </summary>
    public static Member CurrentMember { get; set; } = new();

    /// <summary>
    /// 是否需要刷新会员信息标志 - 控制会员信息的更新时机
    ///
    /// 触发条件：
    /// 1. 会员进行投注操作后
    /// 2. 会员余额发生变化后
    /// 3. 会员信息被管理员修改后
    /// 4. 系统检测到数据不一致时
    ///
    /// 用途：避免频繁查询数据库，提高系统性能
    /// </summary>
    public static bool NeedToRefreshMemberInfo { get; set; }

    /// <summary>
    /// 网络时间 - 从网络获取的标准时间，用于时间同步
    ///
    /// 默认值：本地当前时间
    /// 实际值：从时间服务器或许可证服务器获取的标准时间
    ///
    /// 用途：
    /// 1. 确保开奖时间的准确性
    /// 2. 防止客户端时间被恶意修改
    /// 3. 统一不同客户端的时间基准
    /// 4. 授权到期时间验证
    /// </summary>
    public static DateTime DateTimeNowInternet { get; set; } = DateTime.Now;

    /// <summary>
    /// 一键返水操作结果 - 存储批量返水操作的执行结果
    ///
    /// 内容格式：通常为JSON字符串或状态描述
    ///
    /// 用途：
    /// 1. 记录批量返水操作的执行状态
    /// 2. 显示操作结果给管理员
    /// 3. 异常情况下的操作回滚依据
    /// 4. 操作日志和审计追踪
    /// </summary>
    public static string OneKeyRebateResult { get; set; } = string.Empty;

    #endregion

    #region 跨平台表情符号适配方法

    /// <summary>
    /// 获取钱包表情符号 - 根据不同聊天平台返回对应的钱包图标
    ///
    /// 功能说明：
    /// 不同聊天平台对表情符号的编码方式不同，需要适配处理：
    ///
    /// 编码方式分类：
    /// 1. Unicode转义序列：如 \\uD83D\\uDCB0（微信360018、391016、391216）
    /// 2. 直接Unicode字符：如 💰（微信391125、QQ、一起聊吧、VoceChat）
    /// 3. 自定义格式：如 [emoji=F09F92B0]（MyQQ平台）
    ///
    /// 使用场景：
    /// - 显示用户余额信息时的装饰图标
    /// - 财务相关消息的视觉标识
    /// - 充值、提现操作的提示信息
    ///
    /// 技术细节：
    /// - Unicode码点：U+1F4B0（💰 MONEY BAG）
    /// - UTF-16编码：\\uD83D\\uDCB0（代理对形式）
    /// - 确保在所有支持的聊天平台中正确显示
    /// </summary>
    /// <returns>返回适配当前聊天平台的钱包表情符号字符串</returns>
    public static string GetFaceIdMoneyBag()
    {
        switch (ChatApp)
        {
            case EnumChatApp.微信360018:
                return "\\uD83D\\uDCB0"; // 微信360018版本使用转义序列
            case EnumChatApp.微信391016:
                return "\\uD83D\\uDCB0"; // 微信391016版本使用转义序列
            case EnumChatApp.微信391125:
                return "💰"; // 微信391125版本支持直接Unicode
            case EnumChatApp.微信391216:
                return "\\uD83D\\uDCB0"; // 微信391216版本使用转义序列
            case EnumChatApp.QQ:
                return "💰"; // QQ平台支持直接Unicode
            case EnumChatApp.MyQQ:
                return "[emoji=F09F92B0]"; // MyQQ平台使用自定义格式
            case EnumChatApp.一起聊吧:
                return "💰"; // 一起聊吧平台支持直接Unicode
            case EnumChatApp.VoceChat:
                return "💰"; // VoceChat平台支持直接Unicode
            default:
                return ""; // 未知平台返回空字符串
        }
    }

    /// <summary>
    /// 获取水滴表情符号 - 根据不同聊天平台返回对应的水滴图标
    ///
    /// 功能说明：
    /// 在彩票系统中，"水"通常指代返水、回水等概念，是重要的财务术语：
    ///
    /// 业务含义：
    /// 1. 返水：根据用户投注金额按比例返还的奖励
    /// 2. 回水：用户可以获得的返还资金
    /// 3. 流水：用户的投注总额统计
    ///
    /// 使用场景：
    /// - 返水比例设置和显示
    /// - 回水金额计算结果展示
    /// - 流水统计报表标识
    /// - 财务结算相关消息
    ///
    /// 技术实现：
    /// - Unicode码点：U+1F4A6（💦 SWEAT DROPLETS）
    /// - UTF-16编码：\\uD83D\\uDCA6（代理对形式）
    /// - 跨平台兼容性处理，确保在所有聊天应用中正确显示
    ///
    /// 编码适配：
    /// - 转义序列版本：适用于需要字符串转义的平台
    /// - 直接Unicode：适用于原生支持Unicode的平台
    /// - 自定义格式：适用于有特殊编码要求的平台
    /// </summary>
    /// <returns>返回适配当前聊天平台的水滴表情符号字符串</returns>
    public static string GetFaceIdWater()
    {
        switch (ChatApp)
        {
            case EnumChatApp.微信360018:
                return "\\uD83D\\uDCA6"; // 微信360018版本使用转义序列
            case EnumChatApp.微信391016:
                return "\\uD83D\\uDCA6"; // 微信391016版本使用转义序列
            case EnumChatApp.微信391125:
                return "💦"; // 微信391125版本支持直接Unicode
            case EnumChatApp.微信391216:
                return "\\uD83D\\uDCA6"; // 微信391216版本使用转义序列
            case EnumChatApp.QQ:
                return "💦"; // QQ平台支持直接Unicode
            case EnumChatApp.MyQQ:
                return "[emoji=F09F92A6]"; // MyQQ平台使用自定义格式
            case EnumChatApp.一起聊吧:
                return "💦"; // 一起聊吧平台支持直接Unicode
            case EnumChatApp.VoceChat:
                return "💦"; // VoceChat平台支持直接Unicode
            default:
                return ""; // 未知平台返回空字符串
        }
    }

    /// <summary>
    /// 获取红叉表情符号 - 根据不同聊天平台返回对应的错误/失败标识图标
    ///
    /// 功能说明：
    /// 红叉符号在彩票机器人系统中用于表示各种失败、错误或否定状态：
    ///
    /// 业务应用场景：
    /// 1. 投注失败提示：
    ///    - 余额不足时的投注失败
    ///    - 超过投注限额的提示
    ///    - 封盘后的投注尝试
    ///
    /// 2. 系统错误提示：
    ///    - 网络连接失败
    ///    - 数据验证错误
    ///    - 权限验证失败
    ///
    /// 3. 操作结果反馈：
    ///    - 撤单失败
    ///    - 充值失败
    ///    - 提现失败
    ///
    /// 4. 开奖结果显示：
    ///    - 投注未中奖（挂）
    ///    - 预测错误
    ///
    /// 技术实现：
    /// - Unicode码点：U+274C（❌ CROSS MARK）
    /// - 部分平台使用双重红叉增强视觉效果
    /// - 确保在所有聊天平台中都有明确的错误标识
    ///
    /// 视觉设计：
    /// - 红色：表示警告、错误、失败
    /// - 叉号：表示否定、拒绝、不通过
    /// - 双叉号：增强错误提示的视觉冲击力
    /// </summary>
    /// <returns>返回适配当前聊天平台的红叉表情符号字符串</returns>
    public static string GetFaceIdChaCha()
    {
        switch (ChatApp)
        {
            case EnumChatApp.微信360018:
                return "\\u274C\\u274C"; // 微信360018版本使用双重红叉转义序列
            case EnumChatApp.微信391016:
                return "\\u274C\\u274C"; // 微信391016版本使用双重红叉转义序列
            case EnumChatApp.微信391125:
                return "❌"; // 微信391125版本支持直接Unicode
            case EnumChatApp.微信391216:
                return "\\u274C\\u274C"; // 微信391216版本使用双重红叉转义序列
            case EnumChatApp.QQ:
                return "❌"; // QQ平台支持直接Unicode
            case EnumChatApp.MyQQ:
                return "[emoji=E29D8C]"; // MyQQ平台使用自定义格式
            case EnumChatApp.一起聊吧:
                return "❌"; // 一起聊吧平台支持直接Unicode
            case EnumChatApp.VoceChat:
                return "❌"; // VoceChat平台支持直接Unicode
            default:
                return ""; // 未知平台返回空字符串
        }
    }

    /// <summary>
    /// 获取绿色对勾表情符号 - 根据不同聊天平台返回对应的成功/确认标识图标
    ///
    /// 功能说明：
    /// 绿色对勾在彩票机器人系统中用于表示各种成功、确认或肯定状态：
    ///
    /// 业务应用场景：
    /// 1. 投注成功确认：
    ///    - 投注订单成功提交
    ///    - 投注金额扣除成功
    ///    - 投注信息验证通过
    ///
    /// 2. 系统操作成功：
    ///    - 用户注册成功
    ///    - 登录验证通过
    ///    - 数据同步完成
    ///
    /// 3. 财务操作确认：
    ///    - 充值到账成功
    ///    - 提现申请通过
    ///    - 返水发放完成
    ///
    /// 4. 开奖结果显示：
    ///    - 投注中奖（中）
    ///    - 预测正确
    ///    - 结算完成
    ///
    /// 5. 状态验证通过：
    ///    - 权限验证成功
    ///    - 数据校验通过
    ///    - 操作授权确认
    ///
    /// 技术实现：
    /// - Unicode码点：U+2705（✅ WHITE HEAVY CHECK MARK）
    /// - 绿色背景的白色对勾，视觉效果清晰
    /// - 跨平台兼容性处理，确保成功状态的一致表达
    ///
    /// 视觉设计：
    /// - 绿色：表示安全、成功、通过
    /// - 对勾：表示确认、正确、完成
    /// - 与红叉形成对比，提供清晰的状态区分
    /// </summary>
    /// <returns>返回适配当前聊天平台的绿色对勾表情符号字符串</returns>
    public static string GetFaceIdGreenDuiGou()
    {
        switch (ChatApp)
        {
            case EnumChatApp.微信360018:
                return "\\u2705"; // 微信360018版本使用转义序列
            case EnumChatApp.微信391016:
                return "\\u2705"; // 微信391016版本使用转义序列
            case EnumChatApp.微信391125:
                return "✅"; // 微信391125版本支持直接Unicode
            case EnumChatApp.微信391216:
                return "\\u2705"; // 微信391216版本使用转义序列
            case EnumChatApp.QQ:
                return "\u2705"; // QQ平台使用Unicode转义
            case EnumChatApp.MyQQ:
                return "[emoji=E29C85]"; // MyQQ平台使用自定义格式
            case EnumChatApp.一起聊吧:
                return "\u2705"; // 一起聊吧平台使用Unicode转义
            case EnumChatApp.VoceChat:
                return "\u2705"; // VoceChat平台使用Unicode转义
            default:
                return ""; // 未知平台返回空字符串
        }
    }

    /// <summary>
    /// 获取星星表情符号 - 根据不同聊天平台返回对应的星级/重要标识图标
    ///
    /// 功能说明：
    /// 星星符号在彩票机器人系统中用于表示重要性、等级或特殊状态：
    ///
    /// 业务应用场景：
    /// 1. 重要消息标识：
    ///    - 系统公告和通知
    ///    - 重要活动信息
    ///    - 紧急状态提醒
    ///
    /// 2. 用户等级显示：
    ///    - VIP用户标识
    ///    - 会员等级区分
    ///    - 特殊权限标记
    ///
    /// 3. 功能亮点突出：
    ///    - 新功能介绍
    ///    - 特色玩法推荐
    ///    - 优惠活动标识
    ///
    /// 4. 开奖结果装饰：
    ///    - 大奖中奖提示
    ///    - 特殊开奖号码
    ///    - 幸运用户标识
    ///
    /// 5. 系统状态指示：
    ///    - 服务正常运行
    ///    - 连接状态良好
    ///    - 数据同步完成
    ///
    /// 技术实现：
    /// - Unicode码点：U+2B50（⭐ WHITE MEDIUM STAR）
    /// - 黄色五角星，具有良好的视觉识别度
    /// - 跨平台兼容性处理，确保重要信息的突出显示
    ///
    /// 视觉设计：
    /// - 黄色：表示重要、醒目、特殊
    /// - 星形：表示等级、荣誉、亮点
    /// - 通用性强：在各种背景下都有良好的可见性
    /// </summary>
    /// <returns>返回适配当前聊天平台的星星表情符号字符串</returns>
    public static string GetFaceIdStar()
    {
        switch (ChatApp)
        {
            case EnumChatApp.微信360018:
                return "\\u2B50"; // 微信360018版本使用转义序列
            case EnumChatApp.微信391016:
                return "\\u2B50"; // 微信391016版本使用转义序列
            case EnumChatApp.微信391125:
                return "⭐"; // 微信391125版本支持直接Unicode
            case EnumChatApp.微信391216:
                return "\\u2B50"; // 微信391216版本使用转义序列
            case EnumChatApp.QQ:
                return "\u2b50"; // QQ平台使用Unicode转义（小写）
            case EnumChatApp.MyQQ:
                return "[emoji=E2AD90]"; // MyQQ平台使用自定义格式
            case EnumChatApp.一起聊吧:
                return "\u2b50"; // 一起聊吧平台使用Unicode转义（小写）
            case EnumChatApp.VoceChat:
                return "\u2b50"; // VoceChat平台使用Unicode转义（小写）
            default:
                return ""; // 未知平台返回空字符串
        }
    }

    #endregion
}