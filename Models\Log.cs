﻿using FreeSql.DataAnnotations;
using Newtonsoft.Json;
using Robot.Enum;

namespace Robot.Models;

[Table(Name = "Log")]
public class Log
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public EnumLogType Type { get; set; }
    public DateTime Time { get; set; } = DateTime.Now;
    public string Title { get; set; } = string.Empty;

    [JsonProperty]
    [Column(DbType = "nvarchar(4000)")]
    public string Content { get; set; } = string.Empty;
}