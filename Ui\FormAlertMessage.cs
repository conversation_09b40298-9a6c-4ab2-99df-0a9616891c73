﻿namespace Robot.Ui
{
    /// <summary>
    /// 警告消息类型枚举
    /// 定义了四种不同类型的提示消息，每种类型对应不同的颜色主题
    /// </summary>
    public enum AlertType
    {
        /// <summary>
        /// 信息提示 - 蓝色主题
        /// 用于显示一般性的信息通知，如操作提示、状态更新等
        /// </summary>
        Info,

        /// <summary>
        /// 成功提示 - 绿色主题
        /// 用于显示操作成功的消息，如保存成功、处理完成等
        /// </summary>
        Success,

        /// <summary>
        /// 警告提示 - 橙色主题
        /// 用于显示需要用户注意的警告信息，如参数错误、操作风险等
        /// </summary>
        Warning,

        /// <summary>
        /// 错误提示 - 红色主题
        /// 用于显示系统错误、操作失败等严重问题
        /// </summary>
        Error
    }

    /// <summary>
    /// 警告窗体动作状态枚举
    /// 定义了警告窗体在显示过程中的三个阶段状态
    /// </summary>
    public enum AlertFormAction
    {
        /// <summary>
        /// 启动阶段 - 窗体正在淡入显示
        /// 在此阶段窗体透明度从0逐渐增加到1，实现淡入效果
        /// </summary>
        Start,

        /// <summary>
        /// 等待阶段 - 窗体完全显示并等待
        /// 在此阶段窗体保持完全不透明状态，等待指定时间后进入关闭阶段
        /// </summary>
        Wait,

        /// <summary>
        /// 关闭阶段 - 窗体正在淡出关闭
        /// 在此阶段窗体透明度从1逐渐减少到0，实现淡出效果后关闭窗体
        /// </summary>
        Close
    }

    /// <summary>
    /// 警告消息窗体类
    ///
    /// 功能概述：
    /// 1. 显示各种类型的提示消息（信息、成功、警告、错误）
    /// 2. 支持多个消息同时显示，自动堆叠排列
    /// 3. 具有淡入淡出的动画效果
    /// 4. 自动定时关闭，无需用户手动操作
    /// 5. 根据消息类型显示不同的颜色主题
    ///
    /// 设计特点：
    /// - 非模态窗体，不阻塞用户操作
    /// - 自动定位到屏幕右下角
    /// - 支持多实例同时显示
    /// - 平滑的透明度动画效果
    /// - 现代化的UI设计
    ///
    /// 使用场景：
    /// - 系统操作结果反馈
    /// - 重要信息通知
    /// - 错误和警告提示
    /// - 状态变更通知
    /// </summary>
    public partial class FormAlertMessage : Form
    {
        #region 私有字段

        /// <summary>
        /// 窗体的X坐标位置
        /// 用于记录窗体在屏幕上的水平位置
        /// </summary>
        private int _x;

        /// <summary>
        /// 窗体的Y坐标位置
        /// 用于记录窗体在屏幕上的垂直位置，支持多个警告窗体的堆叠显示
        /// </summary>
        private int _y;

        /// <summary>
        /// 当前窗体的动作状态
        /// 控制窗体在显示过程中的不同阶段行为（启动、等待、关闭）
        /// </summary>
        private AlertFormAction Action { get; set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// 初始化窗体组件并设置初始透明度为完全透明
        /// </summary>
        public FormAlertMessage()
        {
            // 初始化窗体设计器生成的组件
            InitializeComponent();

            // 设置初始透明度为0（完全透明）
            // 这样窗体在显示时可以实现从透明到不透明的淡入效果
            Opacity = 0.0;
        }

        #endregion

        #region 定时器事件处理

        /// <summary>
        /// 定时器触发事件处理方法
        /// 根据当前窗体的动作状态执行相应的处理逻辑
        ///
        /// 处理流程：
        /// 1. Start状态：执行淡入动画，逐渐增加透明度
        /// 2. Wait状态：保持显示状态，等待指定时间
        /// 3. Close状态：执行淡出动画，逐渐减少透明度直至关闭
        /// </summary>
        /// <param name="sender">事件发送者（定时器对象）</param>
        /// <param name="e">事件参数</param>
        private void timer_Close_Tick(object sender, EventArgs e)
        {
            switch (Action)
            {
                case AlertFormAction.Start:
                    // 处理启动阶段：执行淡入动画
                    HandleStartAction();
                    break;
                case AlertFormAction.Wait:
                    // 处理等待阶段：准备进入关闭阶段
                    HandleWaitAction();
                    break;
                case AlertFormAction.Close:
                    // 处理关闭阶段：执行淡出动画
                    HandleCloseAction();
                    break;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示警告消息的主要方法
        ///
        /// 功能说明：
        /// 1. 设置窗体在屏幕上的初始位置
        /// 2. 设置要显示的消息内容
        /// 3. 根据消息类型设置窗体主题颜色
        /// 4. 显示窗体并启动动画效果
        ///
        /// 调用示例：
        /// var alert = new FormAlertMessage();
        /// alert.Show("操作成功！", AlertType.Success);
        /// </summary>
        /// <param name="message">要显示的消息内容</param>
        /// <param name="type">消息类型，决定窗体的颜色主题</param>
        public void Show(string message, AlertType type)
        {
            // 设置窗体的初始显示位置（屏幕右下角，支持多窗体堆叠）
            SetInitialPosition();

            // 设置消息内容到标签控件
            labelContent.Text = message;

            // 根据消息类型设置窗体的颜色主题
            SetAlertTheme(type);

            // 显示窗体（此时透明度为0，用户看不到）
            Show();

            // 设置动作状态为启动，开始淡入动画
            Action = AlertFormAction.Start;

            // 启动定时器，开始执行动画效果
            timer_Close.Start();
        }

        #endregion

        #region 私有方法 - 主题设置

        /// <summary>
        /// 根据警告类型设置窗体的颜色主题
        ///
        /// 颜色方案：
        /// - Info（信息）：皇家蓝色 - 专业、可信赖的感觉
        /// - Success（成功）：海绿色 - 积极、成功的感觉
        /// - Warning（警告）：深橙色 - 引起注意但不过于刺激
        /// - Error（错误）：深红色 - 明确表示错误或危险
        ///
        /// 使用现代C# switch表达式语法，代码更简洁易读
        /// </summary>
        /// <param name="type">警告消息的类型</param>
        private void SetAlertTheme(AlertType type)
        {
            BackColor = type switch
            {
                AlertType.Info => Color.RoyalBlue, // 信息提示：皇家蓝
                AlertType.Success => Color.SeaGreen, // 成功提示：海绿色
                AlertType.Warning => Color.DarkOrange, // 警告提示：深橙色
                AlertType.Error => Color.DarkRed, // 错误提示：深红色
                _ => BackColor, // 默认情况：保持当前背景色
            };
        }

        #endregion

        #region 私有方法 - 位置设置

        /// <summary>
        /// 设置窗体的初始显示位置
        ///
        /// 定位策略：
        /// 1. 窗体显示在屏幕右下角
        /// 2. 支持最多9个警告窗体同时显示
        /// 3. 多个窗体垂直堆叠排列，避免重叠
        /// 4. 每个窗体之间有5像素的间距
        ///
        /// 算法说明：
        /// - X坐标：屏幕宽度 - 窗体宽度（右对齐）
        /// - Y坐标：屏幕高度 - (窗体高度 × 序号 + 间距 × 序号)
        /// - 通过检查已打开的窗体名称来确定当前窗体的序号
        /// </summary>
        private void SetInitialPosition()
        {
            // 设置为手动定位模式，允许程序控制窗体位置
            StartPosition = FormStartPosition.Manual;

            // 循环查找可用的窗体序号（1-9）
            for (int i = 1; i < 10; i++)
            {
                // 检查是否已存在同名的警告窗体
                if (Application.OpenForms["alert" + i] == null)
                {
                    // 设置当前窗体的名称，用于标识和避免重复
                    Name = "alert" + i;

                    // 计算X坐标：屏幕右边缘减去窗体宽度
                    _x = Screen.PrimaryScreen!.WorkingArea.Width - Width;

                    // 计算Y坐标：从屏幕底部向上堆叠
                    // 公式：屏幕高度 - (窗体高度 × 序号 + 间距 × 序号)
                    _y = Screen.PrimaryScreen.WorkingArea.Height - Height * i - 5 * i;

                    // 设置窗体位置
                    Location = new Point(_x, _y);

                    // 找到可用位置后退出循环
                    break;
                }
            }
        }

        #endregion

        #region 私有方法 - 动画处理

        /// <summary>
        /// 处理启动阶段的动画效果
        ///
        /// 功能说明：
        /// 1. 设置较快的定时器间隔（50毫秒）实现平滑动画
        /// 2. 每次调用增加0.1的透明度，实现淡入效果
        /// 3. 当透明度达到1.0时，切换到等待阶段
        /// 4. 在等待阶段设置较长的定时器间隔（6秒）
        ///
        /// 动画参数：
        /// - 定时器间隔：50ms（每秒20帧，保证动画流畅）
        /// - 透明度增量：0.1（从0到1需要10步，约500ms完成淡入）
        /// - 等待时间：6000ms（6秒显示时间）
        /// </summary>
        private void HandleStartAction()
        {
            // 设置快速定时器间隔，用于平滑的淡入动画
            timer_Close.Interval = 50;

            // 增加透明度，实现淡入效果
            AdjustOpacity(0.1);

            // 检查是否已完全显示
            if (Opacity >= 1.0)
            {
                // 切换到等待阶段
                Action = AlertFormAction.Wait;

                // 设置等待时间为6秒
                timer_Close.Interval = 6000;
            }
        }

        /// <summary>
        /// 处理等待阶段的逻辑
        ///
        /// 功能说明：
        /// 在等待阶段，窗体保持完全不透明状态显示6秒钟
        /// 等待时间结束后，直接切换到关闭阶段开始淡出动画
        ///
        /// 设计理念：
        /// 给用户足够的时间阅读消息内容，同时避免消息停留时间过长
        /// 影响用户体验
        /// </summary>
        private void HandleWaitAction()
        {
            // 等待时间结束，直接进入关闭阶段
            Action = AlertFormAction.Close;
        }

        /// <summary>
        /// 处理关闭阶段的动画效果
        ///
        /// 功能说明：
        /// 1. 设置较快的定时器间隔（50毫秒）实现平滑动画
        /// 2. 每次调用减少0.1的透明度，实现淡出效果
        /// 3. 当透明度达到0.0时，关闭窗体
        ///
        /// 动画参数：
        /// - 定时器间隔：50ms（与淡入动画保持一致）
        /// - 透明度减量：-0.1（从1到0需要10步，约500ms完成淡出）
        ///
        /// 资源管理：
        /// 窗体关闭时会自动释放相关资源，包括定时器
        /// </summary>
        private void HandleCloseAction()
        {
            // 设置快速定时器间隔，用于平滑的淡出动画
            timer_Close.Interval = 50;

            // 减少透明度，实现淡出效果
            AdjustOpacity(-0.1);

            // 检查是否已完全透明
            if (Opacity <= 0.0)
            {
                // 关闭窗体，释放资源
                Close();
            }
        }

        /// <summary>
        /// 调整窗体透明度的辅助方法
        ///
        /// 功能说明：
        /// 1. 根据传入的变化量调整当前透明度
        /// 2. 使用Math.Clamp确保透明度值在有效范围内（0.0-1.0）
        /// 3. 避免透明度超出有效范围导致的异常
        ///
        /// 技术特点：
        /// - 使用.NET 6+的Math.Clamp方法，代码更简洁
        /// - 自动处理边界情况，提高代码健壮性
        /// - 支持正负变化量，可用于淡入和淡出
        ///
        /// 参数说明：
        /// change > 0：增加透明度（淡入效果）
        /// </summary>
        /// <param name="change">透明度变化量，范围通常为-0.1到0.1</param>
        private void AdjustOpacity(double change)
        {
            // 使用Math.Clamp限制透明度在0.0到1.0之间
            // 这样可以避免透明度超出有效范围的问题
            Opacity = Math.Clamp(Opacity + change, 0.0, 1.0);
        }

        #endregion
    }
}