﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

[Table(Name = "Member")]
public class Member
{
    [Column(IsPrimary = true)] public string Account { get; set; } = string.Empty;
    public string 昵称 { get; set; } = string.Empty;
    public string 备注名 { get; set; } = string.Empty;
    public decimal Balance { get; set; }
    public decimal 回水比例 { get; set; }
    public bool 是否假人 { get; set; }
    public string 拉手上级 { get; set; } = string.Empty;
    public decimal 返利比例 { get; set; } = 0;
}