﻿using FreeSql.DataAnnotations;

namespace Robot.Models;

[Table(Name = "ReceiveMessage")]
public class ReceiveMessage
{
    [Column(IsPrimary = true)] public string Id { get; set; } = Guid.NewGuid().ToString();
    public DateTime ReceiveTime { get; set; } = DateTime.Now;
    public string MsgTime { get; set; } = string.Empty;
    public string GroupId { get; set; } = string.Empty;
    public string GroupName { get; set; } = string.Empty;
    public string Account { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public bool IsRead { get; set; } = false;
    public DateTime PreTime { get; set; }
    public string Remark { get; set; } = string.Empty;
}