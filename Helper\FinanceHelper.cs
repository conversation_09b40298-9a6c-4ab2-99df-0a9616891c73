﻿using Robot.ChatPlatform;
using Robot.Enum;

namespace Robot.Helper;

public static class FinanceHelper
{
    /// <summary>
    /// 处理一键返水
    /// </summary>
    public static async void OneKeyRebate()
    {
        try
        {
            CommonHelper.OneKeyRebateResult = string.Empty;
            await RobotHelper.RebateHandler();

            if (!string.IsNullOrEmpty(CommonHelper.OneKeyRebateResult))
            {
                CommonHelper.OneKeyRebateResult += "管理已操作全部返水,请各位注意查收.";
                await ChatHelper.SendGroupMessage(CommonHelper.OneKeyRebateResult);
                await DbHelper.AddLog(EnumLogType.机器人, "操作提醒", "一键返水完毕.");
            }

            await Task.Run(() => { MessageBox.Show(@"一键返水完毕."); });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "OneKeyRebateError", ex.ToString());
        }
    }
}