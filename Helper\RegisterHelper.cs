﻿using System.Management;
using AiHelper;
using Newtonsoft.Json;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 眼珠注册秘钥帮助类
/// </summary>
public static class RegisterHelper
{
    // 注册秘钥
    public static string RegistrationKey { get; set; } = string.Empty;

    // 私钥信息
    private static string PrivateKey => "<RSAKeyValue><Modulus>96XtQN5R+rLbosHi6dsBQ71s/tvCx3azf7Znqu0mJbZEZdUd3JKwgtyqyiJK6Vpu516awH4nb1q6zXBcPwJLe4rPJmCxXQriCHtnQYD4VDWMF8HWRCxqDP/EFOem/0jgwUb0bWO+k21z7r6u3IHPIDAECCtmVo7uoIx3o0Ihylk=</Modulus><Exponent>AQAB</Exponent><P>/IBOhFPw+BqQzW51a8e+DNchVqYDL0qc+brWzVKkLWHum/Fv/SHG95eYW7mgiWoIEvQ0wuUil+f7GlL5QiLzZw==</P><Q>+xRnqa/KIwxGGROVxhwq/r9IdKMK6RLUPi5GE8vOMDFX2kp6h1Gh3Ek4bg3+AqZyMrlo9KCKaZxDtKuPkg58Pw==</Q><DP>C6af+Nsms0wGgnQmVuLQkicbErR2UShQNfW5E6BMkwuBxxBM6pE0bRhBn3nhPnX2CWnbRWLtRbUekQhjF/ok4Q==</DP><DQ>JvRrgPA9NGLbHVjki/Ai25Nh8na5YqABPgR/6uJcAy4kq0GS8RvBOSRDFRAmsNKSNTx/BDVERE5yUr2rcMW+kQ==</DQ><InverseQ>2nbVo+9q9gzVvqfYYXeFD2n/js+fzmBbqdD+0+XcWahpGJxxArAhSRsfbuxBnR/QENWL+CpcTKeQ9bcch9GVIQ==</InverseQ><D>zWfMz58W9A9h6S9T3JAQDnWRcBorBnJIE8fhWH52w5TYSnZn3Em3KfOJnTqwU7j0f+eSB9MbNltU0Y0+lex7WY1qTSFbrxM+erN7In6Y+wHEitQS8CXVB2jPECWrIOcSTqnMQJCgsebLhW9aVaP8y3UA1C7kyv1GnWcgiXFiiHk=</D></RSAKeyValue>";

    // 注册信息
    public static RegisterInfo MyRegisterInfo { get; set; } = new();

    /// <summary>
    /// 验证注册码
    /// </summary>
    /// <param name="registrationKey"></param>
    /// <returns></returns>
    public static bool ValidateRegistrationKey(string registrationKey)
    {
        try
        {
            // 注册秘钥异常
            if (string.IsNullOrWhiteSpace(registrationKey)) return false;

            // 使用私钥解密注册码
            string? decryptedDataStr = Ai.RsaDecrypt(registrationKey, PrivateKey);
            // Debug.WriteLine(@"解密后的数据: " + decryptedDataStr);

            // 把字符串decryptedDataStr转换成json对象
            MyRegisterInfo = JsonConvert.DeserializeObject<RegisterInfo>(decryptedDataStr)!;

            // 验证机器标识符是否一致
            string machineCode = GetMachineCode();
            if (MyRegisterInfo.MachineCode != machineCode)
            {
                MyRegisterInfo = new RegisterInfo { ExpireTime = DateTime.Now.AddYears(-1) };
                return false;
            }

            // 验证时间是否有效在有效期内
            if (MyRegisterInfo.ExpireTime < CommonHelper.DateTimeNowInternet)
            {
                return false;
            }
        }
        catch (Exception)
        {
            MyRegisterInfo = new RegisterInfo { ExpireTime = DateTime.Now.AddYears(-1) };
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取机器码
    /// </summary>
    /// <returns></returns>
    public static string GetMachineCode()
    {
        string machineIdentifier = GetComputerSystemProductUuid();
        return machineIdentifier;
        // using SHA256 sha256 = SHA256.Create();
        // byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineIdentifier));
        // string hashStr = BitConverter.ToString(hashBytes).Replace("-", "").ToUpper();
        // Debug.WriteLine(@"机器码: " + hashStr);
        // return hashStr;
    }

    /// <summary>
    /// 获取计算机系统产品UUID
    /// </summary>
    /// <returns></returns>
    private static string GetComputerSystemProductUuid()
    {
        ManagementObjectSearcher searcher = new ManagementObjectSearcher(@"SELECT UUID FROM Win32_ComputerSystemProduct");
        foreach (ManagementBaseObject? obj in searcher.Get()) return obj["UUID"].ToString()!.Trim();

        return string.Empty;
    }
}