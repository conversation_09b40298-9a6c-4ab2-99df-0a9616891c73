﻿using System.Diagnostics;
using AiHelper;
using Flurl.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.ChatPlatform;

/// <summary>
/// WeChatHelper391125From大表舅
/// </summary>
public static class WeChatHelper391125
{
    private static string Host { get; set; } = "http://127.0.0.1:1234";

    #region 获取Robot信息

    /// <summary>
    /// 获取Robot信息
    /// </summary>
    public static async Task GetRobotInfo()
    {
        try
        {
            /*
             * {
                    "code": 1,
                    "data": {
                    "account": "phusun",
                    "city": "",
                    "country": "BM",
                    "currentDataPath": "C:\\Users\\<USER>\\Documents\\WeChat Files\\wxid_3us1uo1fcgen21\\",
                    "dataSavePath": "C:\\Users\\<USER>\\Documents\\WeChat Files\\wxid_3us1uo1fcgen21\\",
                    "dbKey": "09867509ede842fa8b13ee61427d85cd6c7393ca446f4765ab0ac86bbb40d9dd",
                    "headImage": "https://wx.qlogo.cn/mmhead/ver_1/CWPLvsOEVrj0nT0L6GnuAMzticzrfgZcY1tOVZZ2aX4fjW1PaofSLciaOjL6ibck2b8lqESEOZp3wLia8JdibgCtoQz9pTOdb8GJ7bZ3TN6vDDewrldU2BGxGcx30hYjdu1Rw/0",
                    "mobile": "***********",
                    "name": "Phusun",
                    "province": "",
                    "signature": "一念起，万水千山；一念起，沧海桑田。",
                    "wxid": "wxid_3us1uo1fcgen21"
                    },
                    "msg": "success"
                }
             */
            string url = $"{Host}/api/get_self_info";
            string response = await url.WithTimeout(TimeSpan.FromSeconds(3)).GetAsync().ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("data") && response.Contains("mobile"))
            {
                JObject jObject = JObject.Parse(response);
                // string account = jObject["data"]!["account"]!.ToString();
                RobotHelper.RobotInfo.Account = jObject["data"]!["wxid"]!.ToString();
                RobotHelper.RobotInfo.NickName = jObject["data"]!["name"]!.ToString();
                // RobotHelper.RobotInfo.AvatarUrl = jObject["data"]!["headImage"]!.ToString();
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetRobotInfoError", ex.ToString());
        }
    }

    #endregion

    #region 获取所有群

    /// <summary>
    /// 获取所有群
    /// </summary>
    public static async Task GetGroup()
    {
        try
        {
            string url = $"{Host}/api/get_frien_lists";
            string response = await url.WithTimeout(TimeSpan.FromSeconds(3)).GetAsync().ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("data"))
            {
                JObject jObject = JObject.Parse(response);
                JArray jArray = (JArray)jObject["data"]!;
                foreach (JToken jToken in jArray)
                {
                    if (!jToken.ToString().Contains("@chatroom"))
                    {
                        continue;
                    }

                    string wxid = jToken["wxid"]!.ToString();
                    string groupName = jToken["nickname"]!.ToString();
                    if (wxid.EndsWith("@chatroom"))
                    {
                        RobotHelper.GroupDic.TryAdd(wxid, groupName);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetGroupError", ex.ToString());
        }
    }

    #endregion

    #region 获取昵称

    /// <summary>
    /// 获取昵称
    /// </summary>
    /// <param name="wxid"></param>
    public static async Task<string> GetNick(string wxid)
    {
        try
        {
            /*
             * {
                  "big_head": "https://wx.qlogo.cn/mmhead/ver_1/6aYC4G8Ncqus84LHnGDeJgzGXK0qSYwyicUa4oowzkiaXChJu3woyic1m6UBgXWLtu9VrHM6qoWW8WiazWn3yO4KI73icSZmAU4sn8ElGWTbCQCI/132",
                  "city": "",
                  "contry": "",
                  "nick_name": "Jianhan.",
                  "province": "",
                  "remark": "",
                  "sign": "",
                  "small_head": "https://wx.qlogo.cn/mmhead/ver_1/6aYC4G8Ncqus84LHnGDeJgzGXK0qSYwyicUa4oowzkiaXChJu3woyic1m6UBgXWLtu9VrHM6qoWW8WiazWn3yO4KI73icSZmAU4sn8ElGWTbCQCI/0",
                  "sns_img": "",
                  "v3": "",
                  "wxh": "",
                  "wxid": "wxid_dqwp5xz4y79h22"
                }
             */
            string url = $"{Host}/api/get_contact_cache";
            string response = await url.PostJsonAsync(new
            {
                wxid = $"{wxid}"
            }).ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("big_head"))
            {
                JObject jObject = JObject.Parse(response);
                string? nickName = jObject["nick_name"]?.ToString();
                // Debug.WriteLine($"{wxid}:{nickName}");
                return nickName ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetNickError", ex.ToString());
        }

        return string.Empty;
    }

    #endregion

    #region 发送群信息

    /// <summary>
    /// 发送信息
    /// </summary>
    /// <param name="wxId"></param>
    /// <param name="msgContent"></param>
    public static async Task SendGroupMessage(string wxId, string msgContent)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            // await DbHelper.AddLog(EnumLogType.Robot, "SendGroupMessageError", $@"未指定工作群,信息发送失败:{Ai.中括号左}{msgContent}{Ai.中括号右}");
            return;
        }

        try
        {
            string url = $"{Host}/api/send_text_msg";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    wxid = wxId,
                    msg = msgContent
                })
                .ReceiveString();

            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", ex.ToString());
        }
    }

    #endregion

    #region 发送群信息并且@某人

    /// <summary>
    /// 发送群信息并且@某人
    /// </summary>
    /// <param name="wxId"></param>
    /// <param name="msgContent"></param>
    /// <param name="atAccount"></param>
    public static async Task SendGroupMessage(string wxId, string msgContent, string atAccount)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            // Task<Member> taskMember = DbHelper.GetMember(atAccount);
            // await DbHelper.AddLog(EnumLogType.Robot, "SendGroupMessageError", $@"未指定工作群,给{Ai.中括号左}{taskMember.Result.Account}*{taskMember.Result.备注名}{Ai.中括号右}信息发送失败:{Ai.中括号左}{msgContent}{Ai.中括号右}");
            return;
        }

        try
        {
            string url = $"{Host}/api/send_at_text";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    wxids = atAccount,
                    chatRoomId = wxId,
                    msg = msgContent
                })
                .ReceiveString();

            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", ex.ToString());
        }
    }

    #endregion

    #region 发送图片

    /// <summary>
    /// 发送图片
    /// </summary>
    public static async Task SendImage(string wxId, string imgPath)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            // await DbHelper.AddLog(EnumLogType.Robot, "SendGroupMessageError", $@"未指定工作群,信息发送失败:{Ai.中括号左}{imgPath}{Ai.中括号右}");
            return;
        }

        try
        {
            string url = $"{Host}/api/send_image_msg";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    wxid = wxId,
                    image_path = imgPath
                })
                .ReceiveString();
            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息-图片", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendImageError", ex.ToString());
        }
    }

    #endregion

    public static async Task GetMsgList()
    {
        try
        {
            string url = $"{Host}/api/recive_msg";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3))
                .GetAsync()
                .ReceiveString();
            if (!string.IsNullOrWhiteSpace(response) && response.Contains("content"))
            {
                /*
                 * {
                      "content": "wxid_3us1uo1fcgen21:\n2",
                      "createTime": 1727322246,
                      "displayFullContent": "Phusun : 2",
                      "fromUser": "48826697247@chatroom",
                      "msgId": 4234990745846417203,
                      "msgSequence": 766186383,
                      "pid": 24088,
                      "signature": "\u003cmsgsource\u003e\n\t\u003cbizflag\u003e0\u003c/bizflag\u003e\n\t\u003cpua\u003e1\u003c/pua\u003e\n\t\u003csilence\u003e0\u003c/silence\u003e\n\t\u003cmembercount\u003e3\u003c/membercount\u003e\n\t\u003csignature\u003eV1_GYpAMo8+|v1_GYpAMo8+\u003c/signature\u003e\n\t\u003ctmp_node\u003e\n\t\t\u003cpublisher-id\u003e\u003c/publisher-id\u003e\n\t\u003c/tmp_node\u003e\n\u003c/msgsource\u003e\n",
                      "toUser": "wxid_m694qf420vyq22",
                      "type": 1
                    }
                 */
                // 将JSON字符串反序列化为JObject对象
                WeChatMsg? wxMsg = JsonConvert.DeserializeObject<WeChatMsg>(response);

                // 过滤掉不符合要求的消息
                if (wxMsg == null
                    || string.IsNullOrWhiteSpace(wxMsg.Content)
                    || wxMsg.Content.Contains("撤回了一条消息")
                    || wxMsg.Content.Contains("移出了群聊")
                    || wxMsg.Content.Contains("加入了群聊"))
                {
                    // "撤回了一条消息",
                    // "你将\XXX\"移出了群聊",
                    // [CDATA[你邀请\"XXX\"加入了群聊  ]]
                    return;
                }

                // 筛选指定群消息
                if (wxMsg.FromUser == RobotHelper.WorkGroupId)
                {
                    Debug.WriteLine(wxMsg.Content);
                    Debug.WriteLine(ConvertTimestampToBj(long.Parse(wxMsg.CreateTime)));
                    Debug.WriteLine(wxMsg.FromUser);
                    Debug.WriteLine(wxMsg.MsgId);
                    Debug.WriteLine(wxMsg.ToUser);

                    // 定义接收消息
                    ReceiveMessage msg = new ReceiveMessage
                    {
                        Id = wxMsg.MsgId,
                        GroupId = wxMsg.FromUser,
                        Account = Ai.GetTextLeft(wxMsg.Content, ":").Trim(),
                        Content = Ai.GetTextRight(wxMsg.Content, ":").Trim(),
                        MsgTime = wxMsg.CreateTime,
                    };
                    msg.GroupName = RobotHelper.GroupDic[msg.GroupId];

                    // 判断开始状态,记录进数据库,(未开始的状态下则不记录)
                    if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService])
                    {
                        await DbHelper.FSql.Insert<ReceiveMessage>()
                            .AppendData(msg)
                            .ExecuteIdentityAsync();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "WeChatHelper391125.GetMsgListError", ex.ToString());
        }
    }

    private static DateTime ConvertTimestampToBj(long timestamp)
    {
        // 将10位时间戳转为DateTime对象 (Unix时间戳是秒，需要乘以1000转为毫秒)
        DateTime utcDateTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).UtcDateTime;

        // 将UTC时间转换为北京时间 (UTC+8)
        DateTime bjDateTime = utcDateTime.AddHours(8);

        return bjDateTime;
    }
}

public class WeChatMsg
{
    public string Content { get; set; } = string.Empty;
    public string CreateTime { get; set; } = string.Empty;
    public string FromUser { get; set; } = string.Empty;
    public string MsgId { get; set; } = string.Empty;
    public string ToUser { get; set; } = string.Empty;
}