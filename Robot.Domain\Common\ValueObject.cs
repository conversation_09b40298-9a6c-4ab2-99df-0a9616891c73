namespace Robot.Domain.Common;

/// <summary>
/// 值对象基类
/// </summary>
public abstract class ValueObject
{
    /// <summary>
    /// 获取相等性组件
    /// </summary>
    protected abstract IEnumerable<object> GetEqualityComponents();
    
    /// <summary>
    /// 判断相等性
    /// </summary>
    public override bool Equals(object? obj)
    {
        if (obj == null || obj.GetType() != GetType())
        {
            return false;
        }
        
        var other = (ValueObject)obj;
        
        return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
    }
    
    /// <summary>
    /// 获取哈希码
    /// </summary>
    public override int GetHashCode()
    {
        return GetEqualityComponents()
            .Select(x => x?.GetHashCode() ?? 0)
            .Aggregate((x, y) => x ^ y);
    }
    
    /// <summary>
    /// 相等操作符
    /// </summary>
    public static bool operator ==(ValueObject left, ValueObject right)
    {
        return Equals(left, right);
    }
    
    /// <summary>
    /// 不相等操作符
    /// </summary>
    public static bool operator !=(ValueObject left, ValueObject right)
    {
        return !Equals(left, right);
    }
}
