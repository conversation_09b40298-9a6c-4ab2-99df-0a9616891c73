﻿using AiHelper;
using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.ChatPlatform;

/// <summary>
/// WeChatHelper391016From千寻
/// </summary>
public static class WeChatHelper391016
{
    private static string Host { get; set; } = @"http://127.0.0.1:7777";

    #region 获取Robot信息

    /// <summary>
    /// 获取Robot信息
    /// </summary>
    public static async Task GetRobotInfo()
    {
        try
        {
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0003",
                    data = new { }
                })
                .ReceiveString();
            
            if (!string.IsNullOrEmpty(response) && response.Contains("result"))
            {
                JObject jObj = JObject.Parse(response);
                RobotHelper.RobotInfo.Account = jObj["result"]?["wxid"]?.ToString()!;
                RobotHelper.RobotInfo.NickName = jObj["result"]?["nick"]?.ToString()!;
                // RobotHelper.RobotInfo.AvatarUrl = jObj["result"]?["avatarUrl"]?.ToString()!;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetRobotInfoError", ex.ToString());
        }
    }

    #endregion

    #region 获取所有群

    /// <summary>
    /// 获取所有群
    /// </summary>
    public static async Task GetGroup()
    {
        try
        {
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0006",
                    data = new { type = 2 } // 1=从缓存中获取，2=重新遍历二叉树并刷新缓存
                })
                .ReceiveString();

            if (!string.IsNullOrEmpty(response) && response.Contains("操作成功"))
            {
                JObject jObj = JObject.Parse(response);
                JArray jsonObj = JArray.Parse(jObj["result"]?.ToString() ?? string.Empty);
                foreach (JToken jToken in jsonObj)
                {
                    string? groupId = jToken["wxid"]?.ToString();
                    string? groupName = jToken["nick"]?.ToString();
                    if (groupId != null && groupName != null)
                    {
                        RobotHelper.GroupDic.TryAdd(groupId, groupName);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetGroupError", ex.ToString());
        }
    }

    #endregion

    #region 获取昵称

    /// <summary>
    /// 获取昵称
    /// </summary>
    public static async Task<string> GetNick(string wxId)
    {
        try
        {
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0004",
                    data = new { wxid = wxId }
                })
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("操作成功"))
            {
                JObject jObject = JObject.Parse(response);
                string? nickName = jObject["result"]?["nick"]?.ToString();
                return nickName ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetNickError", ex.ToString());
        }

        return string.Empty;
    }

    #endregion

    #region 发送群信息

    /// <summary>
    /// 发送信息
    /// </summary>
    /// <param name="wxId"></param>
    /// <param name="msgContent"></param>
    public static async Task SendGroupMessage(string wxId, string msgContent)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", $@"未指定工作群,信息发送失败:{Ai.中括号左}{msgContent}{Ai.中括号右}");
            return;
        }

        try
        {
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0001",
                    data = new
                    {
                        wxid = wxId,
                        msg = msgContent
                    }
                })
                .ReceiveString();
            
            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", ex.ToString());
        }
    }

    #endregion

    #region 发送群信息并且@某人

    /// <summary>
    /// 发送群信息并且@某人
    /// </summary>
    /// <param name="wxId"></param>
    /// <param name="msgContent"></param>
    /// <param name="atAccount"></param>
    public static async Task SendGroupMessage(string wxId, string msgContent, string atAccount)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            Task<Member> taskMember = DbHelper.GetMember(atAccount);
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", $@"未指定工作群,给{Ai.中括号左}{taskMember.Result.Account}*{taskMember.Result.备注名}{Ai.中括号右}信息发送失败:{Ai.中括号左}{msgContent}{Ai.中括号右}");
            return;
        }

        try
        {
            // Debug.WriteLine($@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] 构建请求数据");
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0001",
                    data = new
                    {
                        wxid = wxId,
                        msg = $"[@,wxid={atAccount},nick=阿,isAuto=true]{msgContent}"
                    }
                })
                .ReceiveString();
            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", ex.ToString());
        }
    }

    #endregion

    #region 发送图片

    /// <summary>
    /// 发送图片
    /// </summary>
    public static async Task SendImage(string wxId, string imgPath)
    {
        if (string.IsNullOrEmpty(wxId))
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessageError", $@"未指定工作群,信息发送失败:{Ai.中括号左}{imgPath}{Ai.中括号右}");
            return;
        }

        try
        {
            string url = $"{Host}/wechat/httpapi";
            string? response = await url
                .WithHeader("Content-Type", "application/json")
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostJsonAsync(new
                {
                    type = "Q0010",
                    data = new
                    {
                        wxid = wxId,
                        path = imgPath
                    }
                })
                .ReceiveString();

            await DbHelper.AddLog(EnumLogType.机器人, "发送群消息-图片", response ?? "空");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendImageError", ex.ToString());
        }
    }

    #endregion
}