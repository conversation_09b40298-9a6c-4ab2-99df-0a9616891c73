using Robot.Domain.Common;
using Robot.Domain.ValueObjects;
using Robot.Shared.Enums;

namespace Robot.Domain.Entities;

/// <summary>
/// 投注订单实体
/// </summary>
public class BetOrder : BaseEntity
{
    /// <summary>
    /// 私有构造函数，用于ORM
    /// </summary>
    private BetOrder() { }
    
    /// <summary>
    /// 创建投注订单
    /// </summary>
    public BetOrder(
        string account,
        string issue,
        BetLotteryType betLotteryType,
        string content,
        Money betAmount,
        decimal odds,
        OrderType orderType,
        string fromMessageId = "")
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException("账号不能为空", nameof(account));
        if (string.IsNullOrWhiteSpace(issue))
            throw new ArgumentException("期号不能为空", nameof(issue));
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("投注内容不能为空", nameof(content));
        if (betAmount.Value <= 0)
            throw new ArgumentException("投注金额必须大于0", nameof(betAmount));
        if (odds <= 0)
            throw new ArgumentException("赔率必须大于0", nameof(odds));
            
        Account = account;
        Issue = issue;
        BetLotteryType = betLotteryType;
        Content = content;
        BetAmount = betAmount;
        Odds = odds;
        OrderType = orderType;
        FromMessageId = fromMessageId;
        
        WinLoseStatus = BetWinLoseStatus.NotDrawn;
        Status = BetOrderStatus.Accepted;
        RebateStatus = RebateStatus.NotRebated;
        DrawResult = 0;
        SettlementAmount = Money.Zero;
        RebateRate = 0m;
        RebateAmount = Money.Zero;
        
        // 添加订单创建事件
        AddDomainEvent(new BetOrderCreatedEvent(Id, Account, Issue, BetAmount, DateTime.Now));
    }
    
    /// <summary>
    /// 会员账号
    /// </summary>
    public string Account { get; private set; } = string.Empty;
    
    /// <summary>
    /// 期号
    /// </summary>
    public string Issue { get; private set; } = string.Empty;
    
    /// <summary>
    /// 投注彩票类型
    /// </summary>
    public BetLotteryType BetLotteryType { get; private set; }
    
    /// <summary>
    /// 投注内容
    /// </summary>
    public string Content { get; private set; } = string.Empty;
    
    /// <summary>
    /// 投注金额
    /// </summary>
    public Money BetAmount { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 赔率
    /// </summary>
    public decimal Odds { get; private set; }
    
    /// <summary>
    /// 开奖结果
    /// </summary>
    public int DrawResult { get; private set; }
    
    /// <summary>
    /// 中奖状态
    /// </summary>
    public BetWinLoseStatus WinLoseStatus { get; private set; }
    
    /// <summary>
    /// 结算金额
    /// </summary>
    public Money SettlementAmount { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 订单状态
    /// </summary>
    public BetOrderStatus Status { get; private set; }
    
    /// <summary>
    /// 回水状态
    /// </summary>
    public RebateStatus RebateStatus { get; private set; }
    
    /// <summary>
    /// 回水比例
    /// </summary>
    public decimal RebateRate { get; private set; }
    
    /// <summary>
    /// 回水金额
    /// </summary>
    public Money RebateAmount { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 来源消息ID
    /// </summary>
    public string FromMessageId { get; private set; } = string.Empty;
    
    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; private set; }
    
    /// <summary>
    /// 结算订单
    /// </summary>
    public void Settle(int drawResult, BetWinLoseStatus winLoseStatus)
    {
        if (Status != BetOrderStatus.Accepted)
            throw new InvalidOperationException("只有已受理的订单才能结算");
            
        DrawResult = drawResult;
        WinLoseStatus = winLoseStatus;
        
        // 计算结算金额
        SettlementAmount = winLoseStatus switch
        {
            BetWinLoseStatus.Win => BetAmount.Multiply(Odds),
            BetWinLoseStatus.Draw => BetAmount,
            BetWinLoseStatus.Lose => Money.Create(-BetAmount.Value),
            _ => Money.Zero
        };
        
        Status = BetOrderStatus.Settled;
        MarkAsUpdated();
        
        // 添加结算事件
        AddDomainEvent(new BetOrderSettledEvent(Id, Account, Issue, WinLoseStatus, SettlementAmount, DateTime.Now));
    }
    
    /// <summary>
    /// 用户撤销订单
    /// </summary>
    public void CancelByUser()
    {
        if (Status != BetOrderStatus.Accepted)
            throw new InvalidOperationException("只有已受理的订单才能撤销");
            
        Status = BetOrderStatus.UserCancelled;
        MarkAsUpdated();
        
        // 添加撤销事件
        AddDomainEvent(new BetOrderCancelledEvent(Id, Account, Issue, "用户撤销", DateTime.Now));
    }
    
    /// <summary>
    /// 管理员撤销订单
    /// </summary>
    public void CancelByAdmin(string reason = "")
    {
        if (Status != BetOrderStatus.Accepted)
            throw new InvalidOperationException("只有已受理的订单才能撤销");
            
        Status = BetOrderStatus.AdminCancelled;
        MarkAsUpdated();
        
        // 添加撤销事件
        AddDomainEvent(new BetOrderCancelledEvent(Id, Account, Issue, $"管理员撤销: {reason}", DateTime.Now));
    }
    
    /// <summary>
    /// 设置回水
    /// </summary>
    public void SetRebate(decimal rebateRate, Money rebateAmount)
    {
        if (Status != BetOrderStatus.Settled)
            throw new InvalidOperationException("只有已结算的订单才能设置回水");
        if (RebateStatus == RebateStatus.Rebated)
            throw new InvalidOperationException("订单已经回水");
            
        RebateRate = rebateRate;
        RebateAmount = rebateAmount;
        RebateStatus = RebateStatus.Rebated;
        MarkAsUpdated();
        
        // 添加回水事件
        AddDomainEvent(new BetOrderRebatedEvent(Id, Account, Issue, RebateAmount, DateTime.Now));
    }
}

/// <summary>
/// 投注订单创建事件
/// </summary>
public record BetOrderCreatedEvent(long OrderId, string Account, string Issue, Money BetAmount, DateTime OccurredOn) : IDomainEvent;

/// <summary>
/// 投注订单结算事件
/// </summary>
public record BetOrderSettledEvent(long OrderId, string Account, string Issue, BetWinLoseStatus WinLoseStatus, Money SettlementAmount, DateTime OccurredOn) : IDomainEvent;

/// <summary>
/// 投注订单撤销事件
/// </summary>
public record BetOrderCancelledEvent(long OrderId, string Account, string Issue, string Reason, DateTime OccurredOn) : IDomainEvent;

/// <summary>
/// 投注订单回水事件
/// </summary>
public record BetOrderRebatedEvent(long OrderId, string Account, string Issue, Money RebateAmount, DateTime OccurredOn) : IDomainEvent;
