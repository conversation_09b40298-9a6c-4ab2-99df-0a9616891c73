﻿using System.Diagnostics;

namespace Robot;

public static class SingleInstance
{
    private static EventWaitHandle? ProgramStarted { get; set; }
    private const string UniqueAppName = "KingRobot"; // 固定的唯一字符串名称,用于验证是否为唯一实例

    public static bool IsContinue()
    {
        ProgramStarted = new EventWaitHandle(false, EventResetMode.AutoReset, UniqueAppName, out bool createNew);
        if (!createNew)
        {
            ProgramStarted.Set();
        }

        return createNew;
    }

    public static void SetCallback(WaitOrTimerCallback callback, object state)
    {
        Debug.Assert(ProgramStarted != null, nameof(ProgramStarted) + " != null");
        ThreadPool.RegisterWaitForSingleObject(ProgramStarted, callback, state, -1, false);
    }
}