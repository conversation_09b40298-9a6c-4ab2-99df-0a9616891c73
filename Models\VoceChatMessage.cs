﻿namespace Robot.Models;

public class VoceChatMessage
{
    public long CreatedAt { get; set; }
    public MessageDetail Detail { get; set; } = new();
    public string Domain { get; set; } = string.Empty;
    public int FromUid { get; set; }
    public int Mid { get; set; }
    public Target Target { get; set; } = new();
    public string Type { get; set; } = string.Empty;
    public string WidgetId { get; set; } = string.Empty;
}