namespace Robot.Domain.Common;

/// <summary>
/// 基础实体类
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// 实体标识符
    /// </summary>
    public long Id { get; protected set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; protected set; } = DateTime.Now;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; protected set; }
    
    /// <summary>
    /// 标记实体已更新
    /// </summary>
    protected void MarkAsUpdated()
    {
        UpdatedAt = DateTime.Now;
    }
    
    /// <summary>
    /// 领域事件列表
    /// </summary>
    private readonly List<IDomainEvent> _domainEvents = new();
    
    /// <summary>
    /// 获取领域事件
    /// </summary>
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();
    
    /// <summary>
    /// 添加领域事件
    /// </summary>
    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
    
    /// <summary>
    /// 清除领域事件
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// 领域事件接口
/// </summary>
public interface IDomainEvent
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    DateTime OccurredOn { get; }
}
