﻿using AiHelper;
using Robot.Enum;
using Robot.Helper;

namespace Robot.ChatPlatform;

public static class ChatHelper
{
    #region 获取Robot信息

    /// <summary>
    /// 获取Robot信息
    /// </summary>
    public static async Task GetRobotInfo()
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "获取Robot信息" + Ai.中括号右, "获取Robot信息");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    await WeChatHelper360018.GetRobotInfo();
                    break;

                case EnumChatApp.微信391016:
                    await WeChatHelper391016.GetRobotInfo();
                    break;

                case EnumChatApp.微信391125:
                    await WeChatHelper391125.GetRobotInfo();
                    break;

                case EnumChatApp.微信391216:
                    await WeChatHelper391216.GetRobotInfo();
                    break;

                case EnumChatApp.QQ:
                    await <PERSON>q<PERSON><PERSON><PERSON>.GetRobotInfo();
                    break;

                case EnumChatApp.MyQQ:
                    await MyQqHelper.GetRobotInfo();
                    break;

                case EnumChatApp.GoQQ:
                    await GoQqHelper.GetRobotInfo();
                    break;

                case EnumChatApp.LaQQ:
                    await LaQqHelper.GetRobotInfo();
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.GetRobotInfo();
                    break;

                case EnumChatApp.VoceChat:
                    await VoceChatHelper.GetRobotInfo();
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetRobotInfoError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 获取所有群

    /// <summary>
    /// 获取所有群
    /// </summary>
    public static async Task GetGroupDic()
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "获取所有群" + Ai.中括号右, $"获取{Ai.中括号左}{RobotHelper.RobotInfo.Account}{Ai.中括号右}所有群");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    await WeChatHelper360018.GetGroup();
                    break;

                case EnumChatApp.微信391016:
                    await WeChatHelper391016.GetGroup();
                    break;

                case EnumChatApp.微信391125:
                    await WeChatHelper391125.GetGroup();
                    break;

                case EnumChatApp.微信391216:
                    await WeChatHelper391216.GetGroup();
                    break;

                case EnumChatApp.QQ:
                    await QqHelper.GetGroup();
                    break;

                case EnumChatApp.MyQQ:
                    await MyQqHelper.GetGroup();
                    break;

                case EnumChatApp.GoQQ:
                    await GoQqHelper.GetGroup();
                    break;

                case EnumChatApp.LaQQ:
                    await LaQqHelper.GetGroup();
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.GetGroup();
                    break;

                case EnumChatApp.VoceChat:
                    await VoceChatHelper.GetGroup();
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetGroupDicError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 获取昵称

    /// <summary>
    /// 获取昵称
    /// </summary>
    public static async Task<string> GetNick(string account)
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "获取昵称" + Ai.中括号右, $"获取{Ai.中括号左}{account}{Ai.中括号右}的昵称");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    return await WeChatHelper360018.GetNick(account);
                case EnumChatApp.微信391016:
                    return await WeChatHelper391016.GetNick(account);
                case EnumChatApp.微信391125:
                    return await WeChatHelper391125.GetNick(account);
                case EnumChatApp.微信391216:
                    return await WeChatHelper391216.GetNick(account);
                case EnumChatApp.QQ:
                    return await QqHelper.GetNick(account);
                case EnumChatApp.MyQQ:
                    return await MyQqHelper.GetNick(account);
                case EnumChatApp.GoQQ:
                    return await GoQqHelper.GetNick(account);
                case EnumChatApp.LaQQ:
                    return await LaQqHelper.GetNick(account);
                case EnumChatApp.一起聊吧:
                    return await OneChatHelper.GetNick(account);
                case EnumChatApp.VoceChat:
                    return await VoceChatHelper.GetNick(account);
                default:
                    return string.Empty;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetNickError" + Ai.中括号右, ex.ToString());
        }

        return "";
    }

    #endregion

    #region 发送群信息

    /// <summary>
    /// 发送群信息
    /// </summary>
    /// <param name="content"></param>
    public static async Task SendGroupMessage(string content)
    {
        try
        {
            content = content.Replace("台湾", "");
            content = content.Replace("番摊", "");
            content = content.Replace("飞艇", "");
            content = content.Replace("一六八", "168");
            content = content.Trim();
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + $"发送群信息{Ai.中括号左}{RobotHelper.WorkGroupId}{Ai.中括号右}" + Ai.中括号右, content);
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    await WeChatHelper360018.SendGroupMessage(RobotHelper.WorkGroupId, content);
                    break;

                case EnumChatApp.微信391016:
                    await WeChatHelper391016.SendGroupMessage(RobotHelper.WorkGroupId, content);
                    break;

                case EnumChatApp.微信391125:
                    await WeChatHelper391125.SendGroupMessage(RobotHelper.WorkGroupId, content);
                    break;

                case EnumChatApp.微信391216:
                    await WeChatHelper391216.SendGroupMessage(RobotHelper.WorkGroupId, content);
                    break;

                case EnumChatApp.QQ:
                    await QqHelper.SendGroupMessage(content);
                    break;

                case EnumChatApp.MyQQ:
                    await MyQqHelper.SendGroupMessage(content);
                    break;

                case EnumChatApp.GoQQ:
                    await GoQqHelper.SendGroupMessage(content);
                    break;

                case EnumChatApp.LaQQ:
                    await LaQqHelper.SendGroupMessage(content);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendGroupMessage(content);
                    break;

                case EnumChatApp.VoceChat:
                    await VoceChatHelper.SendGroupMessage(content);
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 发送群信息并且@某人

    /// <summary>
    /// 发送群信息并且@某人
    /// </summary>
    /// <param name="content"></param>
    /// <param name="atAccount"></param>
    public static async Task SendGroupMessage(string content, string atAccount)
    {
        try
        {
            content = content.Replace("台湾", "");
            content = content.Replace("番摊", "");
            content = content.Replace("飞艇", "");
            content = content.Replace("一六八", "168");
            content = "\r" + content.Trim();

            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + $"发送群信息并且@{Ai.中括号左}{atAccount}{Ai.中括号右}" + Ai.中括号右, content);
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    await WeChatHelper360018.SendGroupMessage(RobotHelper.WorkGroupId, content, atAccount);
                    break;

                case EnumChatApp.微信391016:
                    await WeChatHelper391016.SendGroupMessage(RobotHelper.WorkGroupId, content, atAccount);
                    break;

                case EnumChatApp.微信391125:
                    await WeChatHelper391125.SendGroupMessage(RobotHelper.WorkGroupId, content, atAccount);
                    break;

                case EnumChatApp.微信391216:
                    await WeChatHelper391216.SendGroupMessage(RobotHelper.WorkGroupId, content, atAccount);
                    break;

                case EnumChatApp.QQ:
                    QqHelper.SendGroupMessage(content, atAccount);
                    break;

                case EnumChatApp.MyQQ:
                    MyQqHelper.SendGroupMessage(content, atAccount);
                    break;

                case EnumChatApp.GoQQ:
                    GoQqHelper.SendGroupMessage(content, atAccount);
                    break;

                case EnumChatApp.LaQQ:
                    LaQqHelper.SendGroupMessage(content, atAccount);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendGroupMessage(content, atAccount);
                    break;

                case EnumChatApp.VoceChat:
                    await VoceChatHelper.SendGroupMessage(content, atAccount);
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 发送图片

    /// <summary>
    /// 发送群信息
    /// </summary>
    /// <param name="imgPath"></param>
    public static async Task SendImage(string imgPath)
    {
        try
        {
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信360018:
                    await WeChatHelper360018.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.微信391016:
                    await WeChatHelper391016.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.微信391125:
                    await WeChatHelper391125.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.微信391216:
                    await WeChatHelper391216.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.QQ:
                    await QqHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.MyQQ:
                    await MyQqHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.GoQQ:
                    await GoQqHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.LaQQ:
                    await LaQqHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;

                case EnumChatApp.VoceChat:
                    await VoceChatHelper.SendImage(RobotHelper.WorkGroupId, imgPath);
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendImageError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 获取消息列表

    /// <summary>
    /// 获取消息列表
    /// </summary>
    public static async Task GetMsgList()
    {
        try
        {
            // await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "获取消息列表" + Ai.中括号右, "获取消息列表");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.微信391125:
                    await WeChatHelper391125.GetMsgList();
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetMsgListError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion
}