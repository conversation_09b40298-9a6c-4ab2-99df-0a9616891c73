﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

[Table(Name = "BetResult")]
public class BetResult
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    public EnumBetLottery BetLottery { get; set; }
    public string Issue { get; set; } = string.Empty;
    public EnumBetResult Result { get; set; } = EnumBetResult.未知;
    public string CreateTime { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
    public string UpdateTime { get; set; } = string.Empty;
}