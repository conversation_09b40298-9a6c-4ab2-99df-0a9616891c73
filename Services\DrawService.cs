﻿using Robot.Config;
using Robot.Helper;
using Robot.Models;

namespace Robot.Services;

public static class DrawService
{
    /// <summary>
    /// 处理开奖信息
    /// </summary>
    public static async Task ProcessDrawInfo(CancellationToken token)
    {
        // 获取开奖信息,先记录原有数量,再获取最新开奖信息,如果数量增加,则有新开奖
        long oldKaiJiangCount = await DbHelper.FSql.Select<KaiJiang>().CountAsync(token);
        await DrawHelper.GetDrawInfo();
        long newKaiJiangCount = await DbHelper.FSql.Select<KaiJiang>().CountAsync(token);

        // 判断是否有新开奖
        if (newKaiJiangCount > oldKaiJiangCount)
        {
            // 取出最后开奖数据
            KaiJiang lastKj = await DbHelper.FSql.Select<KaiJiang>()
                .OrderByDescending(x => x.Issue)
                .FirstAsync(token);

            // 画出图片
            await ImageHelper.DrawOpenDataAsync(lastKj, ImageHelper.DrawImagePath);
            await ImageHelper.DrawTanImageAsync(lastKj, 7, ImageHelper.TanRows7ImagePath);
            await ImageHelper.DrawTanImageAsync(lastKj, 6, ImageHelper.TanRows6ImagePath);
            await ImageHelper.DrawTanImageFullAsync(lastKj, 7, ImageHelper.TanRows77ImagePath);
            await ImageHelper.DrawTanImageFullAsync(lastKj, 6, ImageHelper.TanRows66ImagePath);

            // 结算数据
            await RobotHelper.结算答题数据();
            await RobotHelper.结算飞单数据();

            // 判断是否发送开奖数据图
            if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService] && Setting.Current.是否发送开奖图)
            {
                await Task.Delay(500, token);
                await RobotHelper.发送开奖图Handler();
            }

            // 判断是否发送开奖路子图
            if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService] && Setting.Current.是否发送路子图)
            {
                await Task.Delay(500, token);
                await RobotHelper.发送路子图Handler();
            }
        }
    }
}