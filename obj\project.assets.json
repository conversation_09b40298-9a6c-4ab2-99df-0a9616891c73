{"version": 3, "targets": {"net8.0-windows7.0": {"Autoupdater.NET.Official/1.9.2": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2592.51"}, "compile": {"lib/net8.0-windows7.0/AutoUpdater.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows7.0/AutoUpdater.NET.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"], "resource": {"lib/net8.0-windows7.0/ar/AutoUpdater.NET.resources.dll": {"locale": "ar"}, "lib/net8.0-windows7.0/cs/AutoUpdater.NET.resources.dll": {"locale": "cs"}, "lib/net8.0-windows7.0/da/AutoUpdater.NET.resources.dll": {"locale": "da"}, "lib/net8.0-windows7.0/de/AutoUpdater.NET.resources.dll": {"locale": "de"}, "lib/net8.0-windows7.0/es/AutoUpdater.NET.resources.dll": {"locale": "es"}, "lib/net8.0-windows7.0/fr/AutoUpdater.NET.resources.dll": {"locale": "fr"}, "lib/net8.0-windows7.0/it/AutoUpdater.NET.resources.dll": {"locale": "it"}, "lib/net8.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net8.0-windows7.0/ko/AutoUpdater.NET.resources.dll": {"locale": "ko"}, "lib/net8.0-windows7.0/lv/AutoUpdater.NET.resources.dll": {"locale": "lv"}, "lib/net8.0-windows7.0/nl/AutoUpdater.NET.resources.dll": {"locale": "nl"}, "lib/net8.0-windows7.0/pl/AutoUpdater.NET.resources.dll": {"locale": "pl"}, "lib/net8.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows7.0/pt/AutoUpdater.NET.resources.dll": {"locale": "pt"}, "lib/net8.0-windows7.0/ru/AutoUpdater.NET.resources.dll": {"locale": "ru"}, "lib/net8.0-windows7.0/sk/AutoUpdater.NET.resources.dll": {"locale": "sk"}, "lib/net8.0-windows7.0/sv/AutoUpdater.NET.resources.dll": {"locale": "sv"}, "lib/net8.0-windows7.0/th/AutoUpdater.NET.resources.dll": {"locale": "th"}, "lib/net8.0-windows7.0/tr/AutoUpdater.NET.resources.dll": {"locale": "tr"}, "lib/net8.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll": {"locale": "zh-TW"}, "lib/net8.0-windows7.0/zh/AutoUpdater.NET.resources.dll": {"locale": "zh"}}}, "Costura.Fody/6.0.0": {"type": "package", "dependencies": {"Fody": "6.8.2"}, "compile": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "build": {"build/Costura.Fody.props": {}, "build/Costura.Fody.targets": {}}}, "Flurl/4.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Flurl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Flurl.dll": {"related": ".xml"}}}, "Flurl.Http/4.0.2": {"type": "package", "dependencies": {"Flurl": "4.0.0"}, "compile": {"lib/net6.0/Flurl.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"related": ".xml"}}}, "Fody/6.8.2": {"type": "package", "build": {"build/Fody.targets": {}}}, "FreeSql/3.5.209": {"type": "package", "compile": {"lib/netstandard2.1/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.209": {"type": "package", "dependencies": {"FreeSql": "3.5.209", "Microsoft.Extensions.DependencyInjection": "8.0.0"}, "compile": {"lib/net8.0/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.209": {"type": "package", "dependencies": {"FreeSql": "3.5.209", "System.Data.SQLite.Core": "1.0.119"}, "compile": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Web.WebView2/1.0.2592.51": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"related": ".xml"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"related": ".xml"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"related": ".xml"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"related": ".xml"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"related": ".xml"}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "compile": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x86"}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "compile": {"lib/net8.0-windows7.0/SunnyUI.dll": {}}, "runtime": {"lib/net8.0-windows7.0/SunnyUI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net8.0/SunnyUI.Common.dll": {}}, "runtime": {"lib/net8.0/SunnyUI.Common.dll": {}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "[1.0.119]"}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Autoupdater.NET.Official/1.9.2": {"sha512": "wVB0YMk5Dyc2UMUb46t5TVvzipfgkNHtuKh04wp14k/32nY7wMTz8gSMgiaX9WAHesW60K6J8uGz5G8gyGkEPQ==", "type": "package", "path": "autoupdater.net.official/1.9.2", "files": [".nupkg.metadata", ".signature.p7s", "autoupdater.net.official.1.9.2.nupkg.sha512", "autoupdater.net.official.nuspec", "docs/README.md", "lib/net462/AutoUpdater.NET.dll", "lib/net462/AutoUpdater.NET.pdb", "lib/net462/AutoUpdater.NET.xml", "lib/net462/ar/AutoUpdater.NET.resources.dll", "lib/net462/cs/AutoUpdater.NET.resources.dll", "lib/net462/da/AutoUpdater.NET.resources.dll", "lib/net462/de/AutoUpdater.NET.resources.dll", "lib/net462/es/AutoUpdater.NET.resources.dll", "lib/net462/fr/AutoUpdater.NET.resources.dll", "lib/net462/it/AutoUpdater.NET.resources.dll", "lib/net462/ja-JP/AutoUpdater.NET.resources.dll", "lib/net462/ko/AutoUpdater.NET.resources.dll", "lib/net462/lv/AutoUpdater.NET.resources.dll", "lib/net462/nl/AutoUpdater.NET.resources.dll", "lib/net462/pl/AutoUpdater.NET.resources.dll", "lib/net462/pt-BR/AutoUpdater.NET.resources.dll", "lib/net462/pt/AutoUpdater.NET.resources.dll", "lib/net462/ru/AutoUpdater.NET.resources.dll", "lib/net462/sk/AutoUpdater.NET.resources.dll", "lib/net462/sv/AutoUpdater.NET.resources.dll", "lib/net462/th/AutoUpdater.NET.resources.dll", "lib/net462/tr/AutoUpdater.NET.resources.dll", "lib/net462/zh-TW/AutoUpdater.NET.resources.dll", "lib/net462/zh/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/AutoUpdater.NET.dll", "lib/net5.0-windows7.0/AutoUpdater.NET.pdb", "lib/net5.0-windows7.0/AutoUpdater.NET.xml", "lib/net5.0-windows7.0/ar/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/cs/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/da/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/de/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/es/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/fr/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/it/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/ko/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/lv/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/nl/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/pl/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/pt/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/ru/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/sk/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/sv/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/th/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/tr/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll", "lib/net5.0-windows7.0/zh/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/AutoUpdater.NET.dll", "lib/net6.0-windows7.0/AutoUpdater.NET.pdb", "lib/net6.0-windows7.0/AutoUpdater.NET.xml", "lib/net6.0-windows7.0/ar/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/cs/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/da/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/de/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/es/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/fr/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/it/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/ko/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/lv/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/nl/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/pl/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/pt/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/ru/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/sk/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/sv/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/th/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/tr/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll", "lib/net6.0-windows7.0/zh/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/AutoUpdater.NET.dll", "lib/net7.0-windows7.0/AutoUpdater.NET.pdb", "lib/net7.0-windows7.0/AutoUpdater.NET.xml", "lib/net7.0-windows7.0/ar/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/cs/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/da/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/de/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/es/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/fr/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/it/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/ko/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/lv/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/nl/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/pl/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/pt/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/ru/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/sk/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/sv/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/th/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/tr/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll", "lib/net7.0-windows7.0/zh/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/AutoUpdater.NET.dll", "lib/net8.0-windows7.0/AutoUpdater.NET.pdb", "lib/net8.0-windows7.0/AutoUpdater.NET.xml", "lib/net8.0-windows7.0/ar/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/cs/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/da/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/de/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/es/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/fr/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/it/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/ko/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/lv/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/nl/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/pl/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/pt/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/ru/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/sk/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/sv/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/th/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/tr/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll", "lib/net8.0-windows7.0/zh/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/AutoUpdater.NET.dll", "lib/netcoreapp3.1/AutoUpdater.NET.pdb", "lib/netcoreapp3.1/AutoUpdater.NET.xml", "lib/netcoreapp3.1/ar/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/cs/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/da/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/de/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/es/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/fr/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/it/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/ja-JP/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/ko/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/lv/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/nl/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/pl/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/pt-BR/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/pt/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/ru/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/sk/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/sv/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/th/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/tr/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/zh-TW/AutoUpdater.NET.resources.dll", "lib/netcoreapp3.1/zh/AutoUpdater.NET.resources.dll"]}, "Costura.Fody/6.0.0": {"sha512": "3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "type": "package", "path": "costura.fody/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Costura.Fody.props", "build/Costura.Fody.targets", "costura.fody.6.0.0.nupkg.sha512", "costura.fody.nuspec", "icon.png", "lib/netstandard2.0/Costura.dll", "lib/netstandard2.0/Costura.pdb", "lib/netstandard2.0/Costura.xml", "netclassicweaver/Costura.Fody.dll", "netclassicweaver/Costura.Fody.xcf", "netstandardweaver/Costura.Fody.dll", "netstandardweaver/Costura.Fody.xcf"]}, "Flurl/4.0.0": {"sha512": "rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "type": "package", "path": "flurl/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "flurl.4.0.0.nupkg.sha512", "flurl.nuspec", "icon.png", "lib/net461/Flurl.dll", "lib/net461/Flurl.xml", "lib/net472/Flurl.dll", "lib/net472/Flurl.xml", "lib/netstandard2.0/Flurl.dll", "lib/netstandard2.0/Flurl.xml"]}, "Flurl.Http/4.0.2": {"sha512": "9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "type": "package", "path": "flurl.http/4.0.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "flurl.http.4.0.2.nupkg.sha512", "flurl.http.nuspec", "icon.png", "lib/net461/Flurl.Http.dll", "lib/net461/Flurl.Http.xml", "lib/net6.0/Flurl.Http.dll", "lib/net6.0/Flurl.Http.xml", "lib/netstandard2.0/Flurl.Http.dll", "lib/netstandard2.0/Flurl.Http.xml"]}, "Fody/6.8.2": {"sha512": "sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "type": "package", "path": "fody/6.8.2", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/Fody.targets", "fody.6.8.2.nupkg.sha512", "fody.nuspec", "netclassictask/Fody.dll", "netclassictask/FodyCommon.dll", "netclassictask/FodyHelpers.dll", "netclassictask/FodyIsolated.dll", "netclassictask/Mono.Cecil.Pdb.dll", "netclassictask/Mono.Cecil.Pdb.pdb", "netclassictask/Mono.Cecil.Rocks.dll", "netclassictask/Mono.Cecil.Rocks.pdb", "netclassictask/Mono.Cecil.dll", "netclassictask/Mono.Cecil.pdb", "netstandardtask/Fody.dll", "netstandardtask/FodyCommon.dll", "netstandardtask/FodyHelpers.dll", "netstandardtask/FodyIsolated.dll", "netstandardtask/Mono.Cecil.Pdb.dll", "netstandardtask/Mono.Cecil.Pdb.pdb", "netstandardtask/Mono.Cecil.Rocks.dll", "netstandardtask/Mono.Cecil.Rocks.pdb", "netstandardtask/Mono.Cecil.dll", "netstandardtask/Mono.Cecil.pdb", "readme.md"]}, "FreeSql/3.5.209": {"sha512": "fCMWYHPYwLrX+DYyY02XcsFPy9DqNjJpcNBAoJBo86996WBwNidjPEjBUsY26kXfYRZTwO4PxkP1E8ABMeGpEA==", "type": "package", "path": "freesql/3.5.209", "files": [".nupkg.metadata", ".signature.p7s", "freesql.3.5.209.nupkg.sha512", "freesql.nuspec", "lib/net40/FreeSql.dll", "lib/net40/FreeSql.pdb", "lib/net40/FreeSql.xml", "lib/net45/FreeSql.dll", "lib/net45/FreeSql.pdb", "lib/net45/FreeSql.xml", "lib/net451/FreeSql.dll", "lib/net451/FreeSql.pdb", "lib/net451/FreeSql.xml", "lib/netstandard2.0/FreeSql.dll", "lib/netstandard2.0/FreeSql.pdb", "lib/netstandard2.0/FreeSql.xml", "lib/netstandard2.1/FreeSql.dll", "lib/netstandard2.1/FreeSql.pdb", "lib/netstandard2.1/FreeSql.xml", "logo.png", "readme.md"]}, "FreeSql.DbContext/3.5.209": {"sha512": "Bv33k0J0yx55lyHxxsou6rV35b7i/S07OxVPDxXjeAIZW2PJ3ODoatu9IwWJ4HDqQe29vKheY/olnj6M0/QaUw==", "type": "package", "path": "freesql.dbcontext/3.5.209", "files": [".nupkg.metadata", ".signature.p7s", "freesql.dbcontext.3.5.209.nupkg.sha512", "freesql.dbcontext.nuspec", "lib/net40/FreeSql.DbContext.dll", "lib/net40/FreeSql.DbContext.pdb", "lib/net40/FreeSql.DbContext.xml", "lib/net45/FreeSql.DbContext.dll", "lib/net45/FreeSql.DbContext.pdb", "lib/net45/FreeSql.DbContext.xml", "lib/net5.0/FreeSql.DbContext.dll", "lib/net5.0/FreeSql.DbContext.pdb", "lib/net5.0/FreeSql.DbContext.xml", "lib/net6.0/FreeSql.DbContext.dll", "lib/net6.0/FreeSql.DbContext.pdb", "lib/net6.0/FreeSql.DbContext.xml", "lib/net7.0/FreeSql.DbContext.dll", "lib/net7.0/FreeSql.DbContext.pdb", "lib/net7.0/FreeSql.DbContext.xml", "lib/net8.0/FreeSql.DbContext.dll", "lib/net8.0/FreeSql.DbContext.pdb", "lib/net8.0/FreeSql.DbContext.xml", "lib/net9.0/FreeSql.DbContext.dll", "lib/net9.0/FreeSql.DbContext.pdb", "lib/net9.0/FreeSql.DbContext.xml", "lib/netcoreapp3.1/FreeSql.DbContext.dll", "lib/netcoreapp3.1/FreeSql.DbContext.pdb", "lib/netcoreapp3.1/FreeSql.DbContext.xml", "lib/netstandard2.0/FreeSql.DbContext.dll", "lib/netstandard2.0/FreeSql.DbContext.pdb", "lib/netstandard2.0/FreeSql.DbContext.xml", "lib/netstandard2.1/FreeSql.DbContext.dll", "lib/netstandard2.1/FreeSql.DbContext.pdb", "lib/netstandard2.1/FreeSql.DbContext.xml", "logo.png", "readme.md"]}, "FreeSql.Provider.Sqlite/3.5.209": {"sha512": "3T3tx5Hau4x3+rUwwBwpkf4HkpiL6j7nFmAocB/y8GAMBut4P6KEFyTCNvgeoZG24t7abcr60c7gkYPNfHpz5g==", "type": "package", "path": "freesql.provider.sqlite/3.5.209", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.sqlite.3.5.209.nupkg.sha512", "freesql.provider.sqlite.nuspec", "lib/net40/FreeSql.Provider.Sqlite.dll", "lib/net40/FreeSql.Provider.Sqlite.pdb", "lib/net45/FreeSql.Provider.Sqlite.dll", "lib/net45/FreeSql.Provider.Sqlite.pdb", "lib/netstandard2.0/FreeSql.Provider.Sqlite.dll", "lib/netstandard2.0/FreeSql.Provider.Sqlite.pdb", "logo.png", "readme.md"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Web.WebView2/1.0.2592.51": {"sha512": "AC9aWCthS2JvddYA1jl4dFpLBW3GsLRInhp5dkcBzaFXsRehfoUN9olIUsrH41eNaNYd7z9NRvmy81aUA5aD1g==", "type": "package", "path": "microsoft.web.webview2/1.0.2592.51", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.2592.51.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"sha512": "dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "type": "package", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/System.Data.SQLite.dll", "lib/netstandard2.0/System.Data.SQLite.dll.altconfig", "lib/netstandard2.0/System.Data.SQLite.xml", "lib/netstandard2.1/System.Data.SQLite.dll", "lib/netstandard2.1/System.Data.SQLite.dll.altconfig", "lib/netstandard2.1/System.Data.SQLite.xml", "runtimes/linux-x64/native/SQLite.Interop.dll", "runtimes/osx-x64/native/SQLite.Interop.dll", "runtimes/win-x64/native/SQLite.Interop.dll", "runtimes/win-x86/native/SQLite.Interop.dll", "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "stub.system.data.sqlite.core.netstandard.nuspec"]}, "SunnyUI/3.8.2": {"sha512": "CnZ1qpuQtY14jF2RXwS219wPbJFeZHarrEy04p2+oYQL4SeeYpMRwmMnBSUehRAj1cR5WCNHxUF/bH7tCacz7g==", "type": "package", "path": "sunnyui/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.dll", "lib/net472/SunnyUI.dll", "lib/net8.0-windows7.0/SunnyUI.dll", "lib/net9.0-windows7.0/SunnyUI.dll", "sunnyui.3.8.2.nupkg.sha512", "sunnyui.nuspec"]}, "SunnyUI.Common/3.8.2": {"sha512": "OIz2kvCX3HLYPJLilUV7rnK34tyg3DDwz+O0omGIE1tIBoOlrkZllmTWhQz7DbwzECefyCCW/4rPB088EiMocQ==", "type": "package", "path": "sunnyui.common/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.Common.dll", "lib/net472/SunnyUI.Common.dll", "lib/net8.0/SunnyUI.Common.dll", "lib/net9.0/SunnyUI.Common.dll", "sunnyui.common.3.8.2.nupkg.sha512", "sunnyui.common.nuspec"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SQLite.Core/1.0.119": {"sha512": "bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "type": "package", "path": "system.data.sqlite.core/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.1.0.119.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Autoupdater.NET.Official >= 1.9.2", "Costura.Fody >= 6.0.0", "Flurl.Http >= 4.0.2", "FreeSql >= 3.5.209", "FreeSql.DbContext >= 3.5.209", "FreeSql.Provider.Sqlite >= 3.5.209", "Newtonsoft.Json >= 13.0.3", "SunnyUI >= 3.8.2", "System.Management >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\SolutionRobot\\Robot\\Robot.csproj", "projectName": "Robot", "projectPath": "F:\\SolutionRobot\\Robot\\Robot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\SolutionRobot\\Robot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Autoupdater.NET.Official": {"target": "Package", "version": "[1.9.2, )"}, "Costura.Fody": {"suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "Flurl.Http": {"target": "Package", "version": "[4.0.2, )"}, "FreeSql": {"target": "Package", "version": "[3.5.209, )"}, "FreeSql.DbContext": {"target": "Package", "version": "[3.5.209, )"}, "FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.209, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.2, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}