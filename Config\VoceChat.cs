﻿using Sunny.UI;

namespace Robot.Config;

[ConfigFile("Config\\VoceChat.ini")]
public class VoiceChat : IniConfig<VoiceChat>
{
    // 软件名称,注册码
    [ConfigSection("VoceChat")] public string Host { get; set; } = string.Empty;
    [ConfigSection("VoceChat")] public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 设置默认值
    /// </summary>
    public override void SetDefault()
    {
        base.SetDefault();
        Host = "http://**************:3000";
        ApiKey = "把你的API Key写在这里";
    }
}