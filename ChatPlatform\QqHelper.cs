﻿using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Helper;

namespace Robot.ChatPlatform;

/// <summary>
/// QQHelper
/// </summary>
public static class QqHelper
{
    private static string Host { get; set; } = @"http://127.0.0.1:3000";

    #region 获取Robot信息

    /// <summary>
    /// 获取Robot信息
    /// </summary>
    public static async Task GetRobotInfo()
    {
        try
        {
            string url = $"{Host}/get_login_info";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();
            if (!string.IsNullOrWhiteSpace(response) && response.Contains("data"))
            {
                JObject jObj = JObject.Parse(response);
                RobotHelper.RobotInfo.Account = jObj["data"]?["user_id"]?.ToString()!;
                RobotHelper.RobotInfo.NickName = jObj["data"]?["nickname"]?.ToString()!;
                // RobotHelper.RobotInfo.AvatarUrl = $"https://q2.qlogo.cn/headimg_dl?dst_uin={RobotHelper.RobotInfo.Account}&spec=100";
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetRobotInfo", ex.ToString());
        }
    }

    #endregion

    #region 获取所有群

    /// <summary>
    /// 获取所有群
    /// </summary>
    public static async Task GetGroup()
    {
        try
        {
            string url = $"{Host}/get_group_list";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("data"))
            {
                JObject jObj = JObject.Parse(response);
                JArray jArray = JArray.Parse(jObj["data"]?.ToString() ?? string.Empty);
                if (jArray.Any())
                {
                    foreach (JToken token in jArray)
                    {
                        JObject job = (JObject)token;
                        string? groupId = job["group_id"]?.ToString();
                        string? groupName = job["group_name"]?.ToString();
                        if (groupId != null && groupName != null)
                        {
                            RobotHelper.GroupDic.TryAdd(groupId, groupName);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetGroup错误", ex.ToString());
        }
    }

    #endregion

    #region 获取昵称

    /// <summary>
    /// 获取QQ昵称
    /// </summary>
    public static async Task<string> GetNick(string qqStr)
    {
        try
        {
            string url = $"{Host}/get_stranger_info?user_id={qqStr}";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("data"))
            {
                JObject jObj = JObject.Parse(response);
                string? nick = jObj["data"]?["nickname"]?.ToString();
                return nick ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetQqNick", ex.ToString());
        }

        return string.Empty;
    }

    #endregion

    #region 发送群信息

    /// <summary>
    /// 发送群信息
    /// </summary>
    /// <param name="content"></param>
    public static async Task SendGroupMessage(string content)
    {
        try
        {
            string url = $"{Host}/send_group_msg?group_id={RobotHelper.WorkGroupId}&message={content}&auto_escape=false";
            await url.WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }
    }

    #endregion

    #region 发送群信息并且@某人

    /// <summary>
    /// 发送群信息并且@某人
    /// </summary>
    /// <param name="content"></param>
    /// <param name="atAccount"></param>
    public static async void SendGroupMessage(string content, string atAccount)
    {
        try
        {
            content = $"[CQ:at,qq={atAccount}]" + content;
            string url = $"{Host}/send_group_msg?group_id={RobotHelper.WorkGroupId}&message={content}&auto_escape=false";
            await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendGroupMessage", ex.ToString());
        }
    }

    #endregion

    #region 发送图片

    /// <summary>
    /// 发送图片
    /// </summary>
    public static async Task SendImage(string groupId, string imgPath)
    {
        try
        {
            string content = $"[CQ:image,file=file:///{imgPath}]";
            string url = $"{Host}/send_group_msg?group_id={groupId}&message={content}&auto_escape=false";
            await url
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .GetStringAsync();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }
    }

    #endregion
}