﻿using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Sunny.UI;

namespace Robot.Ui;

public partial class FormIndex : UIForm
{
    public FormIndex()
    {
        InitializeComponent();
    }

    private void FormIndex_Load(object sender, EventArgs e)
    {
        Text = $@"{Setting.Current.AppName} - {CommonHelper.AppVersion}";

        // 加载ChatApp
        comboBox_ChatApp.Items.Clear();
        comboBox_ChatApp.Items.Add(EnumChatApp.微信360018);
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信391016);
        comboBox_ChatApp.Items.Add(EnumChatApp.微信391125);
        // comboBox_ChatApp.Items.Add(EnumChatApp.微信391216);
        comboBox_ChatApp.Items.Add(EnumChatApp.QQ);
        comboBox_ChatApp.Items.Add(EnumChatApp.MyQQ);
        // comboBox_ChatApp.Items.Add(EnumChatApp.GoQQ);
        comboBox_ChatApp.Items.Add(EnumChatApp.LaQQ);
        comboBox_ChatApp.Items.Add(EnumChatApp.一起聊吧);
        // comboBox_ChatApp.Items.Add(EnumChatApp.VoceChat);
        comboBox_ChatApp.SelectedIndex = Setting.Current.SelectChatApp;

        // 将机器码复制到剪贴板
        Clipboard.SetText(RegisterHelper.GetMachineCode());
    }

    private void uiButton_Setting_Click(object sender, EventArgs e)
    {
        // 则从本地文件读取授权码
        if (!File.Exists("License.txt"))
        {
            this.ShowErrorDialog("授权码无效或已过期，请联系客服！");
            return;
        }

        // 验证注册码是否过期
        RegisterHelper.RegistrationKey = File.ReadAllText("License.txt");
        bool isReg = RegisterHelper.ValidateRegistrationKey(RegisterHelper.RegistrationKey);
        if (!isReg)
        {
            this.ShowErrorDialog("授权码无效或已过期，请联系客服！");
            return;
        }

        // 确定ChatApp
        if (comboBox_ChatApp.SelectedItem is EnumChatApp.微信360018)
        {
            CommonHelper.ChatApp = EnumChatApp.微信360018;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.微信391016)
        {
            CommonHelper.ChatApp = EnumChatApp.微信391016;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.微信391125)
        {
            CommonHelper.ChatApp = EnumChatApp.微信391125;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.微信391216)
        {
            CommonHelper.ChatApp = EnumChatApp.微信391216;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.QQ)
        {
            CommonHelper.ChatApp = EnumChatApp.QQ;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.MyQQ)
        {
            CommonHelper.ChatApp = EnumChatApp.MyQQ;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.GoQQ)
        {
            CommonHelper.ChatApp = EnumChatApp.GoQQ;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.LaQQ)
        {
            CommonHelper.ChatApp = EnumChatApp.LaQQ;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.一起聊吧)
        {
            CommonHelper.ChatApp = EnumChatApp.一起聊吧;
        }
        else if (comboBox_ChatApp.SelectedItem is EnumChatApp.VoceChat)
        {
            CommonHelper.ChatApp = EnumChatApp.VoceChat;
        }

        // 将选择的ChatApp更新到配置文件
        Setting.Current.SelectChatApp = comboBox_ChatApp.SelectedIndex;
        Setting.Current.Save();

        // 设置 DialogResult 为 OK
        DialogResult = DialogResult.OK;

        // 关闭窗口
        Close();
    }
}