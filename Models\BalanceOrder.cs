﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

public class BalanceOrder
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public DateTime Time { get; set; } = DateTime.Now;
    public string Account { get; set; } = string.Empty;
    public decimal Money { get; set; }
    public EnumBalanceStatus Status { get; set; }
    public DateTime PreTime { get; set; } 
    public string FromMsgId { get; set; } = string.Empty;
}