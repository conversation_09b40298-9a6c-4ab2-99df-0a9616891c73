using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Web;
using AiHelper;
using FreeSql;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// HTTP监听服务,用于接收消息,并处理消息,并把消息加入消息队列,供其他组件处理
/// </summary>
public static class HttpListenerHelper
{
    #region 全局变量属性

    public static HttpListener Listener { get; } = new() { Prefixes = { "http://+:5000/api/" } };
    private static BlockingCollection<string> MessageQueue { get; set; } = new();
    public static CancellationTokenSource Cts { get; set; } = new();

    #endregion

    #region 接收消息

    /// <summary>
    /// 监听消息,把接收到的消息加入 MessageQueue
    /// </summary>
    public static async Task ListeningAsync()
    {
        Listener.Start();
        Debug.WriteLine("HTTP监听服务已启动");

        // 循环监听请求
        while (Listener.IsListening && !Cts.Token.IsCancellationRequested)
        {
            try
            {
                // 获取请求对象
                HttpListenerContext context = await Listener.GetContextAsync().ConfigureAwait(false);

                // 过滤只接收POST请求
                if (context.Request.HttpMethod.Equals("POST"))
                {
                    using StreamReader reader = new StreamReader(context.Request.InputStream, Encoding.UTF8);
                    string message = await reader.ReadToEndAsync(Cts.Token).ConfigureAwait(false);
                    if (!string.IsNullOrEmpty(message))
                    {
                        // Debug.WriteLine("接收到消息: " + message);
                        // MessageQueue.Add(message, Cts.Token);

                        // 如果已经开始服务,则加入消息队列
                        if (CommonHelper.ServiceParamDic[CommonHelper.IsStartService])
                        {
                            // 将消息加入队列
                            MessageQueue.Add(message, Cts.Token);
                        }
                    }
                }

                // 响应请求
                context.Response.StatusCode = (int)HttpStatusCode.OK;
                context.Response.ContentType = "text/html; charset=utf-8";
                byte[] responseBuffer = "<html><body><h1>Hello!</h1></body></html>"u8.ToArray();
                context.Response.ContentLength64 = responseBuffer.Length;
                await using (Stream output = context.Response.OutputStream)
                {
                    await output.WriteAsync(responseBuffer, 0, responseBuffer.Length, Cts.Token).ConfigureAwait(false);
                    await output.FlushAsync(Cts.Token).ConfigureAwait(false);
                }

                context.Response.Close();
            }
            catch (OperationCanceledException ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "HTTP监听服务已接收到取消信号，正在停止...", ex.ToString());
                // break; // 退出循环，停止监听服务
            }
            catch (HttpListenerException ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "HTTP监听服务遇到网络相关错误", ex.ToString());
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "HTTP监听服务遇到未知错误", ex.ToString());
            }
        }

        Listener.Stop();
        await DbHelper.AddLog(EnumLogType.机器人, "HTTP监听服务已停止");
    }

    /// <summary>
    /// 处理MessageQueue中的消息
    /// </summary>
    public static async Task ProcessMessageQueueAsync()
    {
        try
        {
            foreach (string message in MessageQueue.GetConsumingEnumerable(Cts.Token))
            {
                try
                {
                    // 根据平台选择不同的操作
                    switch (CommonHelper.ChatApp)
                    {
                        case EnumChatApp.微信360018:
                            await ProcessWeChatMessage(message);
                            break;

                        case EnumChatApp.微信391016:
                            await ProcessWeChatMessage(message);
                            break;

                        case EnumChatApp.微信391216:
                            await ProcessWeChatMessage(message);
                            break;

                        case EnumChatApp.QQ:
                            await ProcessQqMessage(message);
                            break;

                        case EnumChatApp.MyQQ:
                            await ProcessMyQqMessage(message);
                            break;

                        case EnumChatApp.GoQQ:
                            await ProcessGoQqMessage(message);
                            break;

                        case EnumChatApp.LaQQ:
                            await ProcessLaQqMessage(message);
                            break;

                        case EnumChatApp.一起聊吧:
                            await ProcessOneChatMessage(message);
                            break;

                        case EnumChatApp.VoceChat:
                            await ProcessVoceChatMessage(message);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "ProcessQueueAsync 遇到错误", ex.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ProcessQueueAsync 遇到错误", ex.ToString());
        }
    }

    #endregion

    #region 分类处理不同平台消息

    /// <summary>
    /// 处理微信信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessWeChatMessage(string msgStr)
    {
        try
        {
            // 解析消息
            JObject jObject = JObject.Parse(msgStr);
            if (CommonHelper.ChatApp == EnumChatApp.微信360018)
            {
                jObject = JObject.Parse(jObject["data"]!.ToString());
            }

            string? type = jObject["type"]?.ToString();
            string? fromType = jObject["data"]?["fromType"]?.ToString();
            string? msgType = jObject["data"]?["msgType"]?.ToString();
            string? msgSource = jObject["data"]?["msgSource"]?.ToString();

            // 判断消息类型
            if (type is "D0003" && fromType is "2" && msgType is "1" && msgSource is "0")
            {
                // 构建消息对象
                ReceiveMessage msg = new ReceiveMessage
                {
                    Id = jObject["timestamp"]?.ToString()!,
                    GroupId = jObject["data"]?["fromWxid"]?.ToString()!,
                    Account = jObject["data"]?["finalFromWxid"]?.ToString()!,
                    Content = jObject["data"]?["msg"]?.ToString()!,
                    MsgTime = jObject["data"]?["timeStamp"]?.ToString()!,
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];

                // 二次处理消息内容
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessWeChatMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessWeChatMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessWeChatMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理QQ信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessQqMessage(string msgStr)
    {
        try
        {
            JObject jObject = JObject.Parse(msgStr);
            string? postType = jObject["post_type"]?.ToString();
            string? messageType = jObject["message_type"]?.ToString();
            if (postType is "message" && messageType is "group")
            {
                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    Id = jObject["message_id"]?.ToString()!,
                    GroupId = jObject["group_id"]?.ToString()!,
                    Account = jObject["user_id"]?.ToString()!,
                    Content = jObject["message"]?.ToString()!,
                    // AccountNick = jObject["sender"]?["nickname"]?.ToString()!,
                    MsgTime = jObject["time"]?.ToString()!,
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];

                // 二次处理消息内容
                if (!string.IsNullOrEmpty(msg.Content))
                {
                    JArray jsonObj = JArray.Parse(msg.Content);
                    foreach (JToken jToken in jsonObj)
                    {
                        msg.Content = jToken["data"]?["text"]?.ToString()!;
                        break;
                    }
                }

                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理MyQQ信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessMyQqMessage(string msgStr)
    {
        try
        {
            /*
             * {
                  "MQ_robot": "750909115",
                  "MQ_type": 2,
                  "MQ_type_sub": 0,
                  "MQ_fromID": "49033342",
                  "MQ_fromQQ": "292329830",
                  "MQ_passiveQQ": "750909115",
                  "MQ_msg": "%E6%88%91%E9%9D%A0",
                  "MQ_msgSeq": "2000694914",
                  "MQ_msgID": "28018",
                  "MQ_msgData": "7E E6 78 7E 2C C1 F6 BB 2E 79 4B 8D 1E B5 51 CF 1E DC 00 52 00 00 00 1B 00 09 00 06 03 E9 20 05 31 F0 00 0A 00 04 00 00 00 00 00 0C 00 05 00 01 00 01 01 02 EC 30 7E 01 11 6C 99 66 00 00 6D 72 66 F2 73 D3 00 00 00 67 00 CA 00 01 01 00 00 00 00 00 00 00 4D 53 47 00 00 00 00 00 66 F2 73 D3 77 40 2E 82 00 00 00 00 0A 00 86 02 00 06 E5 AE 8B E4 BD 93 00 00 19 00 56 01 00 53 AA 02 50 08 00 50 00 60 00 68 00 80 01 00 88 01 00 9A 01 3F 08 05 20 C0 50 C8 01 00 F0 01 00 F8 01 00 90 02 00 98 03 00 A0 03 00 B0 03 00 C0 03 00 D0 03 00 E8 03 00 8A 04 02 10 35 90 04 00 B8 04 00 C0 04 00 CA 04 00 F8 04 80 80 08 88 05 00 80 06 00 0E 00 0E 01 00 04 00 00 00 00 07 00 04 00 00 00 01 01 00 09 01 00 06 E6 88 91 E9 9D A0 12 00 25 02 00 09 E6 9D A5 E6 97 A5 E5 8F 91 03 00 01 01 04 00 04 00 00 00 08 05 00 04 00 00 00 01 08 00 04 00 00 00 01",
                  "MQ_timestamp": "109256192"
                }
             */
            JObject jObject = JObject.Parse(msgStr);
            string? mqType = jObject["MQ_type"]?.ToString();
            string? mqFromId = jObject["MQ_fromID"]?.ToString().Trim();
            if (mqType is "2" && !string.IsNullOrWhiteSpace(mqFromId))
            {
                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    Id = jObject["MQ_msgID"]?.ToString()!,
                    GroupId = mqFromId,
                    Account = jObject["MQ_fromQQ"]?.ToString()!,
                    Content = HttpUtility.UrlDecode(jObject["MQ_msg"]?.ToString()!),
                    MsgTime = jObject["MQ_timestamp"]?.ToString()!,
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理GqQQ信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessGoQqMessage(string msgStr)
    {
        try
        {
            JObject jObject = JObject.Parse(msgStr);
            string? postType = jObject["post_type"]?.ToString();
            string? messageType = jObject["message_type"]?.ToString();
            if (postType is "message" && messageType is "group")
            {
                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    Id = jObject["message_id"]?.ToString()!,
                    GroupId = jObject["group_id"]?.ToString()!,
                    Account = jObject["user_id"]?.ToString()!,
                    Content = jObject["message"]?.ToString()!,
                    // AccountNick = jObject["sender"]?["nickname"]?.ToString()!,
                    MsgTime = jObject["time"]?.ToString()!,
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理LaQQ信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessLaQqMessage(string msgStr)
    {
        try
        {
            /*
             {
                "message_type": "group",
                "sub_type": "normal",
                "message_id": 1861809585,
                "group_id": 49033343,
                "user_id": 750909116,
                "anonymous": null,
                "message": [
                {
                "type": "text",
                "data": {
                "text": "就是啊"
                }
                }
                ],
                "raw_message": "就是啊",
                "font": 0,
                "sender": {
                "user_id": 750909116,
                "nickname": "昵称",
                "card": "浩克",
                "sex": "unknown",
                "age": 0,
                "area": "",
                "level": "48",
                "role": "admin",
                "title": ""
                },
                "message_style": {
                "bubble_id": 0,
                "pendant_id": 0,
                "font_id": 0,
                "font_effect_id": 0,
                "is_cs_font_effect_enabled": true,
                "bubble_diy_text_id": 0
                },
                "time": 1742334027,
                "self_id": 292329839,
                "post_type": "message"
                }
             */
            JObject jObject = JObject.Parse(msgStr);
            string? mqType = jObject["message_type"]?.ToString();
            string? mqFromId = jObject["group_id"]?.ToString().Trim();
            if (mqType is "group" && !string.IsNullOrWhiteSpace(mqFromId))
            {
                string content = "";
                JArray jsonObj = JArray.Parse(jObject["message"]?.ToString() ?? string.Empty);
                foreach (JToken jToken in jsonObj)
                {
                    content = jToken["data"]?["text"]?.ToString()!;
                    break;
                }

                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    Id = jObject["message_id"]?.ToString()!,
                    GroupId = mqFromId,
                    Account = jObject["user_id"]?.ToString()!,
                    Content = content,
                    MsgTime = jObject["time"]?.ToString()!
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理OneChat信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessOneChatMessage(string msgStr)
    {
        try
        {
            JObject jObject = JObject.Parse(msgStr);
            // string id = jObject["id"]!.ToString();
            string? uid = jObject["Uid"]?.ToString();
            string? content = jObject["Content"]?.ToString().Trim();
            string? type = jObject["Type"]?.ToString().Trim();
            string time = jObject["CreatedAt"]!.ToString().Trim();

            // long timestamp = long.Parse(jObject["timestamp"]?.ToString().Trim()!);
            // 把13位Unix时间戳转位北京时间
            // DateTime dt = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime.ToLocalTime();

            if (type is "0" && !string.IsNullOrWhiteSpace(content))
            {
                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    // Id = id,
                    GroupId = "OneChat",
                    Account = uid!,
                    Content = content,
                    MsgTime = Ai.GetTextLeft(time.Replace("T", " "), "+"),
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理OneChat信息
    /// </summary>
    /// <param name="msgStr"></param>
    private static async Task ProcessVoceChatMessage(string msgStr)
    {
        try
        {
            JObject jObject = JObject.Parse(msgStr);
            // string id = jObject["id"]!.ToString();
            string? uid = jObject["from_uid"]?.ToString();
            string? content = jObject["detail"]?["content"]?.ToString().Trim();
            string? contentType = jObject["detail"]?["content_type"]?.ToString().Trim();
            long timestamp = long.Parse(jObject["created_at"]?.ToString().Trim()!);
            // 把13位Unix时间戳转位北京时间
            DateTime dt = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(timestamp)).DateTime.ToLocalTime();

            if (contentType is "text/plain" && !string.IsNullOrWhiteSpace(content))
            {
                // 定义接收消息
                ReceiveMessage msg = new ReceiveMessage
                {
                    // Id = id,
                    GroupId = "VoceChat",
                    Account = uid!,
                    Content = content,
                    MsgTime = dt.ToString("yyyy-MM-dd HH:mm:ss:fff")
                };
                msg.GroupName = RobotHelper.GroupDic[msg.GroupId];
                await ProcessMessage(msg);
            }
        }
        catch (JsonReaderException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
        }
        catch (JsonSerializationException ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
        }
    }

    /// <summary>
    /// 处理消息
    /// </summary>
    /// <param name="msg"></param>
    private static async Task ProcessMessage(ReceiveMessage msg)
    {
        try
        {
            // 统一判断消息是否来自指定考试群,并且过滤掉Robot自身消息
            if (msg.GroupId.Equals(RobotHelper.WorkGroupId) && msg.Account != RobotHelper.RobotInfo.Account)
            {
                // 过滤掉空消息,以及消息中带有@的消息
                if (string.IsNullOrWhiteSpace(msg.Content) || msg.Content.Contains("@"))
                {
                    return;
                }

                // 过滤掉包含脚本的消息
                if (msg.Content.Contains("<script>"))
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "检测到可疑内容", msg.Content);
                    return;
                }

                // 使用实体仓储对象的方式来插入数据库
                IBaseRepository<ReceiveMessage> repo = DbHelper.FSql.GetRepository<ReceiveMessage>();
                await repo.InsertAsync(msg, Cts.Token);

                // 记录进数据库
                // long id = await DbHelper.FSql.Insert<ReceiveMessage>().AppendData(msg).ExecuteIdentityAsync(token);
                // Debug.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}/{id}");
            }
        }
        catch (Exception ex)
        {
            // 增加消息特征记录
            await DbHelper.AddLog(EnumLogType.机器人, $"消息处理失败 [ID:{msg.Id}] [平台:{CommonHelper.ChatApp}]", ex.ToString());
        }
    }

    #endregion
}