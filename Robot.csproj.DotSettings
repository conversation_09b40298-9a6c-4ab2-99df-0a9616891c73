﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/DesignerComponentManagerNuGet/DesignerToolboxNuGetNetCoreState/@EntryValue">C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2592.51\lib\netcoreapp3.0\Microsoft.Web.WebView2.WinForms.dll|*C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2592.51\lib\netcoreapp3.0\Microsoft.Web.WebView2.Wpf.dll|*C:\Users\<USER>\.nuget\packages\stub.system.data.sqlite.core.netstandard\1.0.115.5\lib\netstandard2.1\System.Data.SQLite.dll|*C:\Users\<USER>\.nuget\packages\sunnyui\3.8.2\lib\net8.0-windows7.0\SunnyUI.dll|Sunny.UI.UIAnalogMeter;Sunny.UI.UIAvatar;Sunny.UI.UIBarChart;Sunny.UI.UIBattery;Sunny.UI.UIBreadcrumb;Sunny.UI.UIButton;Sunny.UI.UICheckBox;Sunny.UI.UIColorPicker;Sunny.UI.UIComboBox;Sunny.UI.UIComboDataGridView;Sunny.UI.UIComboTreeView;Sunny.UI.UIDataGridViewFooter;Sunny.UI.UIDatePicker;Sunny.UI.UIDatetimePicker;Sunny.UI.UIDigitalLabel;Sunny.UI.UIDoughnutChart;Sunny.UI.UIGifAvatar;Sunny.UI.UIHeaderButton;Sunny.UI.UIHorScrollBar;Sunny.UI.UIHorScrollBarEx;Sunny.UI.UILabel;Sunny.UI.UILedLabel;Sunny.UI.UILight;Sunny.UI.UILine;Sunny.UI.UILineChart;Sunny.UI.UILinkLabel;Sunny.UI.UIMarkLabel;Sunny.UI.UIMenuButton;Sunny.UI.UIMillisecondTimer;Sunny.UI.UINumPadTextBox;Sunny.UI.UIPieChart;Sunny.UI.UIPipe;Sunny.UI.UIProcessBar;Sunny.UI.UIProgressIndicator;Sunny.UI.UIRadioButton;Sunny.UI.UIRoundMeter;Sunny.UI.UIRoundProcess;Sunny.UI.UIRuler;Sunny.UI.UIScrollBar;Sunny.UI.UIScrollingText;Sunny.UI.UISignal;Sunny.UI.UISmoothLabel;Sunny.UI.UIStyleManager;Sunny.UI.UISwitch;Sunny.UI.UISymbolButton;Sunny.UI.UISymbolLabel;Sunny.UI.UIThermometer;Sunny.UI.UITimePicker;Sunny.UI.UITrackBar;Sunny.UI.UITurnSwitch;Sunny.UI.UIValve;Sunny.UI.UIVerificationCode;Sunny.UI.UIVerScrollBarEx;Sunny.UI.UIWaitingBar</s:String></wpf:ResourceDictionary>