using Robot.Domain.Common;
using Robot.Shared.Enums;

namespace Robot.Domain.Entities;

/// <summary>
/// 开奖结果实体
/// </summary>
public class DrawResult : BaseEntity
{
    /// <summary>
    /// 私有构造函数，用于ORM
    /// </summary>
    private DrawResult() { }
    
    /// <summary>
    /// 创建开奖结果
    /// </summary>
    public DrawResult(string issue, LotteryType lotteryType, string drawNumbers, DateTime drawTime)
    {
        if (string.IsNullOrWhiteSpace(issue))
            throw new ArgumentException("期号不能为空", nameof(issue));
        if (string.IsNullOrWhiteSpace(drawNumbers))
            throw new ArgumentException("开奖号码不能为空", nameof(drawNumbers));
            
        Issue = issue;
        LotteryType = lotteryType;
        DrawNumbers = drawNumbers;
        DrawTime = drawTime;
        
        // 添加开奖事件
        AddDomainEvent(new DrawResultCreatedEvent(Issue, LotteryType, DrawNumbers, DrawTime, DateTime.Now));
    }
    
    /// <summary>
    /// 期号
    /// </summary>
    public string Issue { get; private set; } = string.Empty;
    
    /// <summary>
    /// 彩票类型
    /// </summary>
    public LotteryType LotteryType { get; private set; }
    
    /// <summary>
    /// 开奖号码
    /// </summary>
    public string DrawNumbers { get; private set; } = string.Empty;
    
    /// <summary>
    /// 开奖时间
    /// </summary>
    public DateTime DrawTime { get; private set; }
    
    /// <summary>
    /// 获取指定位置的开奖号码
    /// </summary>
    public int GetNumberAtPosition(int position)
    {
        var numbers = DrawNumbers.Split(',');
        if (position < 1 || position > numbers.Length)
            throw new ArgumentOutOfRangeException(nameof(position), "位置超出范围");
            
        if (int.TryParse(numbers[position - 1], out int number))
            return number;
            
        throw new InvalidOperationException($"无法解析位置 {position} 的号码");
    }
    
    /// <summary>
    /// 获取所有开奖号码
    /// </summary>
    public int[] GetAllNumbers()
    {
        return DrawNumbers.Split(',')
            .Select(x => int.TryParse(x, out int number) ? number : 0)
            .ToArray();
    }
    
    /// <summary>
    /// 获取前三位号码
    /// </summary>
    public int[] GetFrontThreeNumbers()
    {
        var numbers = GetAllNumbers();
        return numbers.Take(3).ToArray();
    }
    
    /// <summary>
    /// 获取中三位号码
    /// </summary>
    public int[] GetMiddleThreeNumbers()
    {
        var numbers = GetAllNumbers();
        if (numbers.Length < 6)
            throw new InvalidOperationException("开奖号码不足6位，无法获取中三位");
            
        return numbers.Skip(2).Take(3).ToArray();
    }
    
    /// <summary>
    /// 获取后三位号码
    /// </summary>
    public int[] GetBackThreeNumbers()
    {
        var numbers = GetAllNumbers();
        return numbers.TakeLast(3).ToArray();
    }
}

/// <summary>
/// 开奖结果创建事件
/// </summary>
public record DrawResultCreatedEvent(string Issue, LotteryType LotteryType, string DrawNumbers, DateTime DrawTime, DateTime OccurredOn) : IDomainEvent;
