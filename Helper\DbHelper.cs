﻿using FreeSql;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// 数据库帮助类
/// </summary>
public static class DbHelper
{
    public static object DbLock { get; } = new();

    public static IFreeSql FSql { get; } = new FreeSqlBuilder()
        .UseConnectionString(DataType.Sqlite, @"Data Source=Robot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;")
        // .UseConnectionString(DataType.Sqlite, @"Host=127.0.0.1;Port=5432;Username=postgres;Password=*************; Database=postgres;ArrayNullabilityMode=Always;Pooling=true;Minimum Pool Size=1")
        .UseAutoSyncStructure(true) //自动同步实体结构到数据库，FreeSql不会扫描程序集，只有CRUD时才会生成表。
        .Build(); //请务必定义成 Singleton 单例模式

    #region InitTable,主要用于创建表格

    /// <summary>
    /// InitTable,主要用于创建表格
    /// </summary>
    public static async void InitTable()
    {
        try
        {
            await FSql.Select<Member>().AnyAsync();
            await FSql.Select<BetOrder>().AnyAsync();
            await FSql.Select<HuiZong>().AnyAsync();

            await FSql.Select<AddMoney>().AnyAsync();
            await FSql.Select<SubMoney>().AnyAsync();
            await FSql.Select<Finance>().AnyAsync();

            await FSql.Select<ReceiveMessage>().AnyAsync();
            await FSql.Select<Odds>().AnyAsync();
            await FSql.Select<Log>().AnyAsync();

            await FSql.Select<KaiJiang>().AnyAsync();
        }
        catch (Exception ex)
        {
            await AddLog(EnumLogType.机器人, "InitTable", ex.ToString());
        }
    }

    #endregion

    #region InitOdds,创建默认Odds

    /// <summary>
    /// InitOdds,创建默认赔率
    /// </summary>
    public static async void InitOdds()
    {
        try
        {
            if (!await FSql.Select<Odds>().AnyAsync())
            {
                try
                {
                    Dictionary<string, decimal> oddsDic = new Dictionary<string, decimal>
                    {
                        { "1正", (decimal)1.95 },
                        { "2正", (decimal)1.95 },
                        { "3正", (decimal)1.95 },
                        { "4正", (decimal)1.95 },
                        { "1番", (decimal)3.85 },
                        { "2番", (decimal)3.85 },
                        { "3番", (decimal)3.85 },
                        { "4番", (decimal)3.85 },
                        { "12角", (decimal)1.95 },
                        { "23角", (decimal)1.95 },
                        { "34角", (decimal)1.95 },
                        { "14角", (decimal)1.95 },
                        { "1念2", (decimal)2.9 },
                        { "1念3", (decimal)2.9 },
                        { "1念4", (decimal)2.9 },
                        { "2念1", (decimal)2.9 },
                        { "2念3", (decimal)2.9 },
                        { "2念4", (decimal)2.9 },
                        { "3念1", (decimal)2.9 },
                        { "3念2", (decimal)2.9 },
                        { "3念4", (decimal)2.9 },
                        { "4念1", (decimal)2.9 },
                        { "4念2", (decimal)2.9 },
                        { "4念3", (decimal)2.9 },
                        { "单", (decimal)1.95 },
                        { "双", (decimal)1.95 },
                        // { "大", (decimal)1.95 },
                        // { "小", (decimal)1.95 },
                        // { "三门123", (decimal)1.2 },
                        // { "三门124", (decimal)1.2 },
                        // { "三门134", (decimal)1.2 },
                        // { "三门234", (decimal)1.2 },
                        // { "1无2", (decimal)1.9934 },
                        // { "1无3", (decimal)1.9934 },
                        // { "1无4", (decimal)1.9934 },
                        // { "2无1", (decimal)1.9934 },
                        // { "2无3", (decimal)1.9934 },
                        // { "2无4", (decimal)1.9934 },
                        // { "3无1", (decimal)1.9934 },
                        // { "3无2", (decimal)1.9934 },
                        // { "3无4", (decimal)1.9934 },
                        // { "4无1", (decimal)1.9934 },
                        // { "4无2", (decimal)1.9934 },
                        // { "4无3", (decimal)1.9934 }
                    };

                    List<Odds> oddsList = new List<Odds>();

                    // 固定值
                    int minStake = 1;
                    int maxStake = 30000;
                    int totalStake = 30000;

                    // 实例化对象，确定最低限投、最高限投、总额限投
                    foreach (KeyValuePair<string, decimal> kv in oddsDic)
                    {
                        Odds odds = new Odds
                        {
                            项目 = kv.Key,
                            赔率 = kv.Value,
                            最低限投 = minStake,
                            最高限投 = maxStake,
                            总额限投 = totalStake
                        };
                        if (kv.Key.Equals("大") ||
                            kv.Key.Equals("小") ||
                            kv.Key.Equals("三门123") ||
                            kv.Key.Equals("三门124") ||
                            kv.Key.Equals("三门134") ||
                            kv.Key.Equals("三门234") ||
                            kv.Key.Equals("1无2") ||
                            kv.Key.Equals("1无3") ||
                            kv.Key.Equals("1无4") ||
                            kv.Key.Equals("2无1") ||
                            kv.Key.Equals("2无3") ||
                            kv.Key.Equals("2无4") ||
                            kv.Key.Equals("3无1") ||
                            kv.Key.Equals("3无2") ||
                            kv.Key.Equals("3无4") ||
                            kv.Key.Equals("4无1") ||
                            kv.Key.Equals("4无2") ||
                            kv.Key.Equals("4无3"))
                        {
                            odds.最低限投 = 0;
                            odds.最高限投 = 0;
                        }

                        oddsList.Add(odds);
                    }

                    await FSql.Insert<Odds>().AppendData(oddsList).ExecuteAffrowsAsync();
                }
                catch (Exception ex)
                {
                    await AddLog(EnumLogType.机器人, "InitOdds", ex.ToString());
                }
            }
        }
        catch (Exception e)
        {
            await AddLog(EnumLogType.机器人, "InitOdds", e.ToString());
        }
    }

    #endregion

    #region 根据Account返回Member

    /// <summary>
    /// 根据Account返回Member
    /// </summary>
    /// <returns></returns>
    public static async Task<Member> GetMember(string account)
    {
        return await FSql.Select<Member>()
            .Where(a => a.Account.Equals(account))
            .ToOneAsync();
    }

    #endregion

    #region AddLog

    /// <summary>
    /// AddLog
    /// </summary>
    /// <param name="type"></param>
    /// <param name="title"></param>
    /// <param name="content"></param>
    public static async Task AddLog(EnumLogType type, string title, string? content = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                content = "null";
            }

            Log log = new Log
            {
                Type = type,
                Title = title,
                Content = content
            };

            log.Id = await FSql.Insert<Log>().AppendData(log).ExecuteIdentityAsync();
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("AddLogError.log", ex + Environment.NewLine + Environment.NewLine);
        }
    }

    #endregion
}