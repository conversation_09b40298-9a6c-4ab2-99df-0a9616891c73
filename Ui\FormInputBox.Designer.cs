﻿using System.ComponentModel;

namespace Robot.Ui
{
    partial class FormInputBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            ComponentResourceManager resources = new ComponentResourceManager(typeof(FormInputBox));
            textBox_Value = new TextBox();
            button_Sure = new Button();
            button_Cancel = new Button();
            label_Tips = new Label();
            SuspendLayout();
            // 
            // textBox_Value
            // 
            textBox_Value.BorderStyle = BorderStyle.FixedSingle;
            textBox_Value.Location = new Point(12, 36);
            textBox_Value.Name = "textBox_Value";
            textBox_Value.Size = new Size(351, 23);
            textBox_Value.TabIndex = 0;
            textBox_Value.TextAlign = HorizontalAlignment.Center;
            // 
            // button_Sure
            // 
            button_Sure.Location = new Point(139, 71);
            button_Sure.Name = "button_Sure";
            button_Sure.Size = new Size(100, 30);
            button_Sure.TabIndex = 1;
            button_Sure.Text = "确定";
            button_Sure.UseVisualStyleBackColor = true;
            button_Sure.Click += button_Sure_Click;
            // 
            // button_Cancel
            // 
            button_Cancel.Location = new Point(261, 71);
            button_Cancel.Name = "button_Cancel";
            button_Cancel.Size = new Size(100, 30);
            button_Cancel.TabIndex = 2;
            button_Cancel.Text = "取消";
            button_Cancel.UseVisualStyleBackColor = true;
            button_Cancel.Click += button_Cancel_Click;
            // 
            // label_Tips
            // 
            label_Tips.AutoSize = true;
            label_Tips.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label_Tips.ForeColor = Color.Red;
            label_Tips.Location = new Point(13, 10);
            label_Tips.Name = "label_Tips";
            label_Tips.Size = new Size(55, 19);
            label_Tips.TabIndex = 3;
            label_Tips.Text = "请输入:";
            // 
            // FormInputBox
            // 
            AcceptButton = button_Sure;
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            CancelButton = button_Cancel;
            ClientSize = new Size(375, 113);
            Controls.Add(label_Tips);
            Controls.Add(button_Cancel);
            Controls.Add(button_Sure);
            Controls.Add(textBox_Value);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FormInputBox";
            StartPosition = FormStartPosition.CenterScreen;
            Load += FormInputBox_Load;
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private TextBox textBox_Value;
        private Button button_Sure;
        private Button button_Cancel;
        private Label label_Tips;
    }
}