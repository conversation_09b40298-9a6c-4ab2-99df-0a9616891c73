using Robot.Domain.Entities;

namespace Robot.Domain.Interfaces;

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 会员仓储
    /// </summary>
    IRepository<Member> Members { get; }
    
    /// <summary>
    /// 投注订单仓储
    /// </summary>
    IRepository<BetOrder> BetOrders { get; }
    
    /// <summary>
    /// 开奖结果仓储
    /// </summary>
    IRepository<DrawResult> DrawResults { get; }
    
    /// <summary>
    /// 赔率仓储
    /// </summary>
    IRepository<Odds> Odds { get; }
    
    /// <summary>
    /// 保存更改
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 开始事务
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
