﻿using System.ComponentModel;

namespace Robot.Ui
{
    partial class FormMain
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle8 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle9 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle10 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle11 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle12 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle13 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle14 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle15 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle16 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle17 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle18 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle19 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle20 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle21 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle22 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle23 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle24 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle25 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle26 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle27 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle28 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle29 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle30 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle31 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle32 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle33 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle34 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle35 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle36 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle37 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle38 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle39 = new DataGridViewCellStyle();
            ComponentResourceManager resources = new ComponentResourceManager(typeof(FormMain));
            statusStrip_Foot = new StatusStrip();
            toolStripStatusLabel_CloseTime = new ToolStripStatusLabel();
            toolStripStatusLabel_OpenTime = new ToolStripStatusLabel();
            toolStripStatusLabel_MemberInfo = new ToolStripStatusLabel();
            toolStripStatusLabel_Lottery = new ToolStripStatusLabel();
            toolStripStatusLabel_RobotInfo = new ToolStripStatusLabel();
            tabControl_Main = new TabControl();
            tabPage_Home = new TabPage();
            tableLayoutPanel_Main = new TableLayoutPanel();
            tableLayoutPanel_Body = new TableLayoutPanel();
            tableLayoutPanel1 = new TableLayoutPanel();
            dataGridView_PlatformLog = new DataGridView();
            PlatformLog_Id = new DataGridViewTextBoxColumn();
            PlatformLog_Time = new DataGridViewTextBoxColumn();
            PlatformLog_Title = new DataGridViewTextBoxColumn();
            PlatformLog_Content = new DataGridViewTextBoxColumn();
            dataGridView_SubMoney = new DataGridView();
            SubMoney_Id = new DataGridViewTextBoxColumn();
            SubMoney_Account = new DataGridViewTextBoxColumn();
            SubMoney_RemarkName = new DataGridViewTextBoxColumn();
            SubMoney_Money = new DataGridViewTextBoxColumn();
            SubMoney_Agree = new DataGridViewButtonColumn();
            SubMoney_Refuse = new DataGridViewButtonColumn();
            dataGridView_AddMoney = new DataGridView();
            AddMoney_Id = new DataGridViewTextBoxColumn();
            AddMoney_Account = new DataGridViewTextBoxColumn();
            AddMoney_RemarkName = new DataGridViewTextBoxColumn();
            AddMoney_Money = new DataGridViewTextBoxColumn();
            AddMoney_Agree = new DataGridViewButtonColumn();
            AddMoney_Refuse = new DataGridViewButtonColumn();
            tableLayoutPanel_MemberAndBetOrder = new TableLayoutPanel();
            dataGridView_BetOrderDetail = new DataGridView();
            BetOrderDetail_Account = new DataGridViewTextBoxColumn();
            BetOrderDetail_RemarkName = new DataGridViewTextBoxColumn();
            BetOrderDetail_BetLottery = new DataGridViewTextBoxColumn();
            BetOrderDetail_Issue = new DataGridViewTextBoxColumn();
            BetOrderDetail_Content = new DataGridViewTextBoxColumn();
            BetOrderDetail_Odds = new DataGridViewTextBoxColumn();
            BetOrderDetail_Balance = new DataGridViewTextBoxColumn();
            dataGridView_MemberInfo = new DataGridView();
            panel_HomeMenu = new Panel();
            uiButton_Service = new Sunny.UI.UIButton();
            uiButton_HandToOpen = new Sunny.UI.UIButton();
            uiButton_CancelBetOrder = new Sunny.UI.UIButton();
            uiButton_HandToBet = new Sunny.UI.UIButton();
            checkBox_CancelOrder = new CheckBox();
            comboBox_BetLottery = new ComboBox();
            checkBox_IsDuiChong = new CheckBox();
            checkBox_IsFeiDan = new CheckBox();
            comboBox_WorkGroupId = new ComboBox();
            tabPage_BetOrderReport = new TabPage();
            tableLayoutPanel2 = new TableLayoutPanel();
            dataGridView_BetOrderReport = new DataGridView();
            panel1 = new Panel();
            comboBox_SelectMemberForBetOrder = new ComboBox();
            label4 = new Label();
            comboBox_SelectIssueForBetOrder = new ComboBox();
            label3 = new Label();
            tabPage_BetTotalReport = new TabPage();
            tableLayoutPanel3 = new TableLayoutPanel();
            dataGridView_HuiZongReport = new DataGridView();
            panel2 = new Panel();
            comboBox_SelectIssueForHuiZong = new ComboBox();
            label6 = new Label();
            tabPage_AddMoneyReport = new TabPage();
            tableLayoutPanel4 = new TableLayoutPanel();
            dataGridView_AddMoneyReport = new DataGridView();
            panel3 = new Panel();
            comboBox_SelectObjForAddMoneyReport = new ComboBox();
            label7 = new Label();
            tabPage_SubMoneyReport = new TabPage();
            tableLayoutPanel5 = new TableLayoutPanel();
            dataGridView_SubMoneyReport = new DataGridView();
            panel4 = new Panel();
            comboBox_SelectObjForSubMoneyReport = new ComboBox();
            label8 = new Label();
            tabPage_FinanceReport = new TabPage();
            tableLayoutPanel6 = new TableLayoutPanel();
            dataGridView_FinanceReport = new DataGridView();
            panel5 = new Panel();
            comboBox_SelectObjForFinanceReport = new ComboBox();
            label5 = new Label();
            tabPage_RecMsg = new TabPage();
            tableLayoutPanel7 = new TableLayoutPanel();
            dataGridView_RecMsg = new DataGridView();
            panel6 = new Panel();
            comboBox_SelectMemberForShowRecMsg = new ComboBox();
            label9 = new Label();
            tabPage_Setting = new TabPage();
            tableLayoutPanel8 = new TableLayoutPanel();
            tableLayoutPanel_Odds = new TableLayoutPanel();
            dataGridView_Odds = new DataGridView();
            button_SaveOdds = new Button();
            panel_Setting = new Panel();
            button_ShowTime = new Button();
            groupBox8 = new GroupBox();
            checkBox_AutoHuiShui = new CheckBox();
            checkBox_JiaRenAutoAddMoney = new CheckBox();
            groupBox6 = new GroupBox();
            label2 = new Label();
            comboBox_CloseTime = new ComboBox();
            groupBox5 = new GroupBox();
            button_Clear = new Button();
            checkBox_SaveMemberBalance = new CheckBox();
            checkBox_SaveMemberBaseInfo = new CheckBox();
            groupBox3 = new GroupBox();
            radioButton_ImgType2 = new RadioButton();
            radioButton_ImgType1 = new RadioButton();
            checkBox_6Rows = new CheckBox();
            checkBox_7Rows = new CheckBox();
            groupBox2 = new GroupBox();
            button_CancelBetData = new Button();
            textBox_CancelIssue = new TextBox();
            groupBox1 = new GroupBox();
            button_OneKeyRebate = new Button();
            checkBox_ShowReturnCommissionDetail = new CheckBox();
            button_SaveReturnCommissionPercent = new Button();
            textBox_ReturnCommissionPercent = new TextBox();
            tabPage_Log = new TabPage();
            dataGridView_Log = new DataGridView();
            Log_Id = new DataGridViewTextBoxColumn();
            Log_Type = new DataGridViewTextBoxColumn();
            Log_Time = new DataGridViewTextBoxColumn();
            Log_Title = new DataGridViewTextBoxColumn();
            Log_Content = new DataGridViewTextBoxColumn();
            tabPage_OneChat = new TabPage();
            webView2_OneChat = new Microsoft.Web.WebView2.WinForms.WebView2();
            contextMenuStrip_Menu = new ContextMenuStrip(components);
            notifyIcon_Pass = new NotifyIcon(components);
            notifyIcon_NG = new NotifyIcon(components);
            statusStrip_Foot.SuspendLayout();
            tabControl_Main.SuspendLayout();
            tabPage_Home.SuspendLayout();
            tableLayoutPanel_Main.SuspendLayout();
            tableLayoutPanel_Body.SuspendLayout();
            tableLayoutPanel1.SuspendLayout();
            ((ISupportInitialize)dataGridView_PlatformLog).BeginInit();
            ((ISupportInitialize)dataGridView_SubMoney).BeginInit();
            ((ISupportInitialize)dataGridView_AddMoney).BeginInit();
            tableLayoutPanel_MemberAndBetOrder.SuspendLayout();
            ((ISupportInitialize)dataGridView_BetOrderDetail).BeginInit();
            ((ISupportInitialize)dataGridView_MemberInfo).BeginInit();
            panel_HomeMenu.SuspendLayout();
            tabPage_BetOrderReport.SuspendLayout();
            tableLayoutPanel2.SuspendLayout();
            ((ISupportInitialize)dataGridView_BetOrderReport).BeginInit();
            panel1.SuspendLayout();
            tabPage_BetTotalReport.SuspendLayout();
            tableLayoutPanel3.SuspendLayout();
            ((ISupportInitialize)dataGridView_HuiZongReport).BeginInit();
            panel2.SuspendLayout();
            tabPage_AddMoneyReport.SuspendLayout();
            tableLayoutPanel4.SuspendLayout();
            ((ISupportInitialize)dataGridView_AddMoneyReport).BeginInit();
            panel3.SuspendLayout();
            tabPage_SubMoneyReport.SuspendLayout();
            tableLayoutPanel5.SuspendLayout();
            ((ISupportInitialize)dataGridView_SubMoneyReport).BeginInit();
            panel4.SuspendLayout();
            tabPage_FinanceReport.SuspendLayout();
            tableLayoutPanel6.SuspendLayout();
            ((ISupportInitialize)dataGridView_FinanceReport).BeginInit();
            panel5.SuspendLayout();
            tabPage_RecMsg.SuspendLayout();
            tableLayoutPanel7.SuspendLayout();
            ((ISupportInitialize)dataGridView_RecMsg).BeginInit();
            panel6.SuspendLayout();
            tabPage_Setting.SuspendLayout();
            tableLayoutPanel8.SuspendLayout();
            tableLayoutPanel_Odds.SuspendLayout();
            ((ISupportInitialize)dataGridView_Odds).BeginInit();
            panel_Setting.SuspendLayout();
            groupBox8.SuspendLayout();
            groupBox6.SuspendLayout();
            groupBox5.SuspendLayout();
            groupBox3.SuspendLayout();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            tabPage_Log.SuspendLayout();
            ((ISupportInitialize)dataGridView_Log).BeginInit();
            tabPage_OneChat.SuspendLayout();
            ((ISupportInitialize)webView2_OneChat).BeginInit();
            SuspendLayout();
            // 
            // statusStrip_Foot
            // 
            statusStrip_Foot.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel_CloseTime, toolStripStatusLabel_OpenTime, toolStripStatusLabel_MemberInfo, toolStripStatusLabel_Lottery, toolStripStatusLabel_RobotInfo });
            statusStrip_Foot.Location = new Point(0, 711);
            statusStrip_Foot.Name = "statusStrip_Foot";
            statusStrip_Foot.Size = new Size(1283, 26);
            statusStrip_Foot.TabIndex = 0;
            statusStrip_Foot.Text = "statusStrip1";
            // 
            // toolStripStatusLabel_CloseTime
            // 
            toolStripStatusLabel_CloseTime.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_CloseTime.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_CloseTime.ForeColor = Color.Red;
            toolStripStatusLabel_CloseTime.Name = "toolStripStatusLabel_CloseTime";
            toolStripStatusLabel_CloseTime.Size = new Size(76, 21);
            toolStripStatusLabel_CloseTime.Text = "CloseTime";
            // 
            // toolStripStatusLabel_OpenTime
            // 
            toolStripStatusLabel_OpenTime.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_OpenTime.Name = "toolStripStatusLabel_OpenTime";
            toolStripStatusLabel_OpenTime.Size = new Size(72, 21);
            toolStripStatusLabel_OpenTime.Text = "OpenTime";
            // 
            // toolStripStatusLabel_MemberInfo
            // 
            toolStripStatusLabel_MemberInfo.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_MemberInfo.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_MemberInfo.ForeColor = Color.Red;
            toolStripStatusLabel_MemberInfo.Name = "toolStripStatusLabel_MemberInfo";
            toolStripStatusLabel_MemberInfo.Size = new Size(4, 21);
            // 
            // toolStripStatusLabel_Lottery
            // 
            toolStripStatusLabel_Lottery.BorderSides = ToolStripStatusLabelBorderSides.Right;
            toolStripStatusLabel_Lottery.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_Lottery.ForeColor = Color.Red;
            toolStripStatusLabel_Lottery.Name = "toolStripStatusLabel_Lottery";
            toolStripStatusLabel_Lottery.Size = new Size(1046, 21);
            toolStripStatusLabel_Lottery.Spring = true;
            // 
            // toolStripStatusLabel_RobotInfo
            // 
            toolStripStatusLabel_RobotInfo.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            toolStripStatusLabel_RobotInfo.ForeColor = Color.Red;
            toolStripStatusLabel_RobotInfo.Name = "toolStripStatusLabel_RobotInfo";
            toolStripStatusLabel_RobotInfo.Size = new Size(70, 21);
            toolStripStatusLabel_RobotInfo.Text = "RobotInfo";
            // 
            // tabControl_Main
            // 
            tabControl_Main.Controls.Add(tabPage_Home);
            tabControl_Main.Controls.Add(tabPage_BetOrderReport);
            tabControl_Main.Controls.Add(tabPage_BetTotalReport);
            tabControl_Main.Controls.Add(tabPage_AddMoneyReport);
            tabControl_Main.Controls.Add(tabPage_SubMoneyReport);
            tabControl_Main.Controls.Add(tabPage_FinanceReport);
            tabControl_Main.Controls.Add(tabPage_RecMsg);
            tabControl_Main.Controls.Add(tabPage_Setting);
            tabControl_Main.Controls.Add(tabPage_Log);
            tabControl_Main.Controls.Add(tabPage_OneChat);
            tabControl_Main.Dock = DockStyle.Fill;
            tabControl_Main.Location = new Point(0, 0);
            tabControl_Main.Name = "tabControl_Main";
            tabControl_Main.SelectedIndex = 0;
            tabControl_Main.Size = new Size(1283, 711);
            tabControl_Main.TabIndex = 1;
            tabControl_Main.SelectedIndexChanged += tabControl_Main_SelectedIndexChanged;
            // 
            // tabPage_Home
            // 
            tabPage_Home.Controls.Add(tableLayoutPanel_Main);
            tabPage_Home.Location = new Point(4, 26);
            tabPage_Home.Name = "tabPage_Home";
            tabPage_Home.Size = new Size(1275, 681);
            tabPage_Home.TabIndex = 0;
            tabPage_Home.Text = "▶主控台";
            tabPage_Home.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel_Main
            // 
            tableLayoutPanel_Main.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel_Main.ColumnCount = 1;
            tableLayoutPanel_Main.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.Controls.Add(tableLayoutPanel_Body, 0, 1);
            tableLayoutPanel_Main.Controls.Add(panel_HomeMenu, 0, 0);
            tableLayoutPanel_Main.Dock = DockStyle.Fill;
            tableLayoutPanel_Main.Location = new Point(0, 0);
            tableLayoutPanel_Main.Margin = new Padding(0);
            tableLayoutPanel_Main.Name = "tableLayoutPanel_Main";
            tableLayoutPanel_Main.RowCount = 2;
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.Size = new Size(1275, 681);
            tableLayoutPanel_Main.TabIndex = 1;
            // 
            // tableLayoutPanel_Body
            // 
            tableLayoutPanel_Body.ColumnCount = 2;
            tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 575F));
            tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            tableLayoutPanel_Body.Controls.Add(tableLayoutPanel1, 1, 0);
            tableLayoutPanel_Body.Controls.Add(tableLayoutPanel_MemberAndBetOrder, 0, 0);
            tableLayoutPanel_Body.Dock = DockStyle.Fill;
            tableLayoutPanel_Body.Location = new Point(1, 37);
            tableLayoutPanel_Body.Margin = new Padding(0);
            tableLayoutPanel_Body.Name = "tableLayoutPanel_Body";
            tableLayoutPanel_Body.RowCount = 1;
            tableLayoutPanel_Body.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Body.Size = new Size(1273, 643);
            tableLayoutPanel_Body.TabIndex = 0;
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 1;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Controls.Add(dataGridView_PlatformLog, 0, 2);
            tableLayoutPanel1.Controls.Add(dataGridView_SubMoney, 0, 1);
            tableLayoutPanel1.Controls.Add(dataGridView_AddMoney, 0, 0);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(701, 3);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 3;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 30F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 30F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 40F));
            tableLayoutPanel1.Size = new Size(569, 637);
            tableLayoutPanel1.TabIndex = 2;
            // 
            // dataGridView_PlatformLog
            // 
            dataGridView_PlatformLog.AllowUserToAddRows = false;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = SystemColors.Control;
            dataGridViewCellStyle1.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle1.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dataGridView_PlatformLog.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dataGridView_PlatformLog.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_PlatformLog.Columns.AddRange(new DataGridViewColumn[] { PlatformLog_Id, PlatformLog_Time, PlatformLog_Title, PlatformLog_Content });
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = SystemColors.Window;
            dataGridViewCellStyle2.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle2.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dataGridView_PlatformLog.DefaultCellStyle = dataGridViewCellStyle2;
            dataGridView_PlatformLog.Dock = DockStyle.Fill;
            dataGridView_PlatformLog.Location = new Point(3, 385);
            dataGridView_PlatformLog.Name = "dataGridView_PlatformLog";
            dataGridView_PlatformLog.ReadOnly = true;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = SystemColors.Control;
            dataGridViewCellStyle3.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle3.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.True;
            dataGridView_PlatformLog.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            dataGridView_PlatformLog.Size = new Size(563, 249);
            dataGridView_PlatformLog.TabIndex = 3;
            // 
            // PlatformLog_Id
            // 
            PlatformLog_Id.HeaderText = "Id";
            PlatformLog_Id.Name = "PlatformLog_Id";
            PlatformLog_Id.ReadOnly = true;
            // 
            // PlatformLog_Time
            // 
            PlatformLog_Time.HeaderText = "时间";
            PlatformLog_Time.Name = "PlatformLog_Time";
            PlatformLog_Time.ReadOnly = true;
            // 
            // PlatformLog_Title
            // 
            PlatformLog_Title.HeaderText = "摘要";
            PlatformLog_Title.Name = "PlatformLog_Title";
            PlatformLog_Title.ReadOnly = true;
            // 
            // PlatformLog_Content
            // 
            PlatformLog_Content.HeaderText = "详情";
            PlatformLog_Content.Name = "PlatformLog_Content";
            PlatformLog_Content.ReadOnly = true;
            // 
            // dataGridView_SubMoney
            // 
            dataGridView_SubMoney.AllowUserToAddRows = false;
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = SystemColors.Control;
            dataGridViewCellStyle4.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle4.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoney.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            dataGridView_SubMoney.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_SubMoney.Columns.AddRange(new DataGridViewColumn[] { SubMoney_Id, SubMoney_Account, SubMoney_RemarkName, SubMoney_Money, SubMoney_Agree, SubMoney_Refuse });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = SystemColors.Window;
            dataGridViewCellStyle5.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle5.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            dataGridView_SubMoney.DefaultCellStyle = dataGridViewCellStyle5;
            dataGridView_SubMoney.Dock = DockStyle.Fill;
            dataGridView_SubMoney.Location = new Point(3, 194);
            dataGridView_SubMoney.Name = "dataGridView_SubMoney";
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = SystemColors.Control;
            dataGridViewCellStyle6.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle6.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoney.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            dataGridView_SubMoney.Size = new Size(563, 185);
            dataGridView_SubMoney.TabIndex = 2;
            // 
            // SubMoney_Id
            // 
            SubMoney_Id.HeaderText = "Id";
            SubMoney_Id.Name = "SubMoney_Id";
            // 
            // SubMoney_Account
            // 
            SubMoney_Account.HeaderText = "账号";
            SubMoney_Account.Name = "SubMoney_Account";
            // 
            // SubMoney_RemarkName
            // 
            SubMoney_RemarkName.HeaderText = "备注名";
            SubMoney_RemarkName.Name = "SubMoney_RemarkName";
            // 
            // SubMoney_Money
            // 
            SubMoney_Money.HeaderText = "下分";
            SubMoney_Money.Name = "SubMoney_Money";
            // 
            // SubMoney_Agree
            // 
            SubMoney_Agree.HeaderText = "同意";
            SubMoney_Agree.Name = "SubMoney_Agree";
            SubMoney_Agree.Resizable = DataGridViewTriState.True;
            SubMoney_Agree.SortMode = DataGridViewColumnSortMode.Automatic;
            // 
            // SubMoney_Refuse
            // 
            SubMoney_Refuse.HeaderText = "拒绝";
            SubMoney_Refuse.Name = "SubMoney_Refuse";
            SubMoney_Refuse.Resizable = DataGridViewTriState.True;
            SubMoney_Refuse.SortMode = DataGridViewColumnSortMode.Automatic;
            // 
            // dataGridView_AddMoney
            // 
            dataGridView_AddMoney.AllowUserToAddRows = false;
            dataGridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle7.BackColor = SystemColors.Control;
            dataGridViewCellStyle7.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle7.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoney.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            dataGridView_AddMoney.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_AddMoney.Columns.AddRange(new DataGridViewColumn[] { AddMoney_Id, AddMoney_Account, AddMoney_RemarkName, AddMoney_Money, AddMoney_Agree, AddMoney_Refuse });
            dataGridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle8.BackColor = SystemColors.Window;
            dataGridViewCellStyle8.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle8.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle8.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle8.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = DataGridViewTriState.False;
            dataGridView_AddMoney.DefaultCellStyle = dataGridViewCellStyle8;
            dataGridView_AddMoney.Dock = DockStyle.Fill;
            dataGridView_AddMoney.Location = new Point(3, 3);
            dataGridView_AddMoney.Name = "dataGridView_AddMoney";
            dataGridViewCellStyle9.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = SystemColors.Control;
            dataGridViewCellStyle9.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle9.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle9.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle9.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoney.RowHeadersDefaultCellStyle = dataGridViewCellStyle9;
            dataGridView_AddMoney.Size = new Size(563, 185);
            dataGridView_AddMoney.TabIndex = 1;
            // 
            // AddMoney_Id
            // 
            AddMoney_Id.HeaderText = "Id";
            AddMoney_Id.Name = "AddMoney_Id";
            // 
            // AddMoney_Account
            // 
            AddMoney_Account.HeaderText = "账号";
            AddMoney_Account.Name = "AddMoney_Account";
            // 
            // AddMoney_RemarkName
            // 
            AddMoney_RemarkName.HeaderText = "备注名";
            AddMoney_RemarkName.Name = "AddMoney_RemarkName";
            // 
            // AddMoney_Money
            // 
            AddMoney_Money.HeaderText = "上分";
            AddMoney_Money.Name = "AddMoney_Money";
            // 
            // AddMoney_Agree
            // 
            AddMoney_Agree.HeaderText = "同意";
            AddMoney_Agree.Name = "AddMoney_Agree";
            AddMoney_Agree.Resizable = DataGridViewTriState.True;
            AddMoney_Agree.SortMode = DataGridViewColumnSortMode.Automatic;
            // 
            // AddMoney_Refuse
            // 
            AddMoney_Refuse.HeaderText = "拒绝";
            AddMoney_Refuse.Name = "AddMoney_Refuse";
            AddMoney_Refuse.Resizable = DataGridViewTriState.True;
            AddMoney_Refuse.SortMode = DataGridViewColumnSortMode.Automatic;
            AddMoney_Refuse.Text = "拒绝";
            // 
            // tableLayoutPanel_MemberAndBetOrder
            // 
            tableLayoutPanel_MemberAndBetOrder.ColumnCount = 1;
            tableLayoutPanel_MemberAndBetOrder.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(dataGridView_BetOrderDetail, 0, 1);
            tableLayoutPanel_MemberAndBetOrder.Controls.Add(dataGridView_MemberInfo, 0, 0);
            tableLayoutPanel_MemberAndBetOrder.Dock = DockStyle.Fill;
            tableLayoutPanel_MemberAndBetOrder.Location = new Point(3, 3);
            tableLayoutPanel_MemberAndBetOrder.Name = "tableLayoutPanel_MemberAndBetOrder";
            tableLayoutPanel_MemberAndBetOrder.RowCount = 2;
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));
            tableLayoutPanel_MemberAndBetOrder.RowStyles.Add(new RowStyle(SizeType.Percent, 40F));
            tableLayoutPanel_MemberAndBetOrder.Size = new Size(692, 637);
            tableLayoutPanel_MemberAndBetOrder.TabIndex = 1;
            // 
            // dataGridView_BetOrderDetail
            // 
            dataGridView_BetOrderDetail.AllowUserToAddRows = false;
            dataGridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle10.BackColor = SystemColors.Control;
            dataGridViewCellStyle10.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle10.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle10.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle10.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle10.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderDetail.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle10;
            dataGridView_BetOrderDetail.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_BetOrderDetail.Columns.AddRange(new DataGridViewColumn[] { BetOrderDetail_Account, BetOrderDetail_RemarkName, BetOrderDetail_BetLottery, BetOrderDetail_Issue, BetOrderDetail_Content, BetOrderDetail_Odds, BetOrderDetail_Balance });
            dataGridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle11.BackColor = SystemColors.Window;
            dataGridViewCellStyle11.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle11.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle11.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle11.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle11.WrapMode = DataGridViewTriState.False;
            dataGridView_BetOrderDetail.DefaultCellStyle = dataGridViewCellStyle11;
            dataGridView_BetOrderDetail.Dock = DockStyle.Fill;
            dataGridView_BetOrderDetail.Location = new Point(3, 385);
            dataGridView_BetOrderDetail.Name = "dataGridView_BetOrderDetail";
            dataGridView_BetOrderDetail.ReadOnly = true;
            dataGridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle12.BackColor = SystemColors.Control;
            dataGridViewCellStyle12.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle12.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle12.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderDetail.RowHeadersDefaultCellStyle = dataGridViewCellStyle12;
            dataGridView_BetOrderDetail.Size = new Size(686, 249);
            dataGridView_BetOrderDetail.TabIndex = 2;
            // 
            // BetOrderDetail_Account
            // 
            BetOrderDetail_Account.HeaderText = "账号";
            BetOrderDetail_Account.Name = "BetOrderDetail_Account";
            BetOrderDetail_Account.ReadOnly = true;
            BetOrderDetail_Account.Width = 180;
            // 
            // BetOrderDetail_RemarkName
            // 
            BetOrderDetail_RemarkName.HeaderText = "备注名";
            BetOrderDetail_RemarkName.Name = "BetOrderDetail_RemarkName";
            BetOrderDetail_RemarkName.ReadOnly = true;
            BetOrderDetail_RemarkName.Width = 150;
            // 
            // BetOrderDetail_BetLottery
            // 
            BetOrderDetail_BetLottery.HeaderText = "游戏";
            BetOrderDetail_BetLottery.Name = "BetOrderDetail_BetLottery";
            BetOrderDetail_BetLottery.ReadOnly = true;
            // 
            // BetOrderDetail_Issue
            // 
            BetOrderDetail_Issue.HeaderText = "期号";
            BetOrderDetail_Issue.Name = "BetOrderDetail_Issue";
            BetOrderDetail_Issue.ReadOnly = true;
            // 
            // BetOrderDetail_Content
            // 
            BetOrderDetail_Content.HeaderText = "项目";
            BetOrderDetail_Content.Name = "BetOrderDetail_Content";
            BetOrderDetail_Content.ReadOnly = true;
            BetOrderDetail_Content.Width = 80;
            // 
            // BetOrderDetail_Odds
            // 
            BetOrderDetail_Odds.HeaderText = "赔率";
            BetOrderDetail_Odds.Name = "BetOrderDetail_Odds";
            BetOrderDetail_Odds.ReadOnly = true;
            BetOrderDetail_Odds.Width = 80;
            // 
            // BetOrderDetail_Balance
            // 
            BetOrderDetail_Balance.HeaderText = "金额";
            BetOrderDetail_Balance.Name = "BetOrderDetail_Balance";
            BetOrderDetail_Balance.ReadOnly = true;
            BetOrderDetail_Balance.Width = 80;
            // 
            // dataGridView_MemberInfo
            // 
            dataGridView_MemberInfo.AllowUserToAddRows = false;
            dataGridViewCellStyle13.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle13.BackColor = SystemColors.Control;
            dataGridViewCellStyle13.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle13.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle13.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle13.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle13.WrapMode = DataGridViewTriState.True;
            dataGridView_MemberInfo.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle13;
            dataGridView_MemberInfo.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle14.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle14.BackColor = SystemColors.Window;
            dataGridViewCellStyle14.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle14.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle14.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = DataGridViewTriState.False;
            dataGridView_MemberInfo.DefaultCellStyle = dataGridViewCellStyle14;
            dataGridView_MemberInfo.Dock = DockStyle.Fill;
            dataGridView_MemberInfo.Location = new Point(3, 3);
            dataGridView_MemberInfo.Name = "dataGridView_MemberInfo";
            dataGridViewCellStyle15.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle15.BackColor = SystemColors.Control;
            dataGridViewCellStyle15.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle15.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle15.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle15.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle15.WrapMode = DataGridViewTriState.True;
            dataGridView_MemberInfo.RowHeadersDefaultCellStyle = dataGridViewCellStyle15;
            dataGridView_MemberInfo.Size = new Size(686, 376);
            dataGridView_MemberInfo.TabIndex = 1;
            dataGridView_MemberInfo.VirtualMode = true;
            // 
            // panel_HomeMenu
            // 
            panel_HomeMenu.Controls.Add(uiButton_Service);
            panel_HomeMenu.Controls.Add(uiButton_HandToOpen);
            panel_HomeMenu.Controls.Add(uiButton_CancelBetOrder);
            panel_HomeMenu.Controls.Add(uiButton_HandToBet);
            panel_HomeMenu.Controls.Add(checkBox_CancelOrder);
            panel_HomeMenu.Controls.Add(comboBox_BetLottery);
            panel_HomeMenu.Controls.Add(checkBox_IsDuiChong);
            panel_HomeMenu.Controls.Add(checkBox_IsFeiDan);
            panel_HomeMenu.Controls.Add(comboBox_WorkGroupId);
            panel_HomeMenu.Dock = DockStyle.Fill;
            panel_HomeMenu.Location = new Point(4, 4);
            panel_HomeMenu.Name = "panel_HomeMenu";
            panel_HomeMenu.Size = new Size(1267, 29);
            panel_HomeMenu.TabIndex = 1;
            // 
            // uiButton_Service
            // 
            uiButton_Service.Cursor = Cursors.Hand;
            uiButton_Service.FillColor = Color.FromArgb(230, 80, 80);
            uiButton_Service.FillColor2 = Color.FromArgb(230, 80, 80);
            uiButton_Service.FillHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_Service.FillPressColor = Color.FromArgb(184, 64, 64);
            uiButton_Service.FillSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_Service.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_Service.LightColor = Color.FromArgb(253, 243, 243);
            uiButton_Service.Location = new Point(427, 2);
            uiButton_Service.MinimumSize = new Size(1, 1);
            uiButton_Service.Name = "uiButton_Service";
            uiButton_Service.RectColor = Color.FromArgb(230, 80, 80);
            uiButton_Service.RectHoverColor = Color.FromArgb(235, 115, 115);
            uiButton_Service.RectPressColor = Color.FromArgb(184, 64, 64);
            uiButton_Service.RectSelectedColor = Color.FromArgb(184, 64, 64);
            uiButton_Service.Size = new Size(102, 25);
            uiButton_Service.Style = Sunny.UI.UIStyle.Custom;
            uiButton_Service.TabIndex = 18;
            uiButton_Service.Text = "开始";
            uiButton_Service.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_Service.Click += uiButton_Service_Click;
            // 
            // uiButton_HandToOpen
            // 
            uiButton_HandToOpen.Cursor = Cursors.Hand;
            uiButton_HandToOpen.FillColor = Color.FromArgb(102, 58, 183);
            uiButton_HandToOpen.FillColor2 = Color.FromArgb(102, 58, 183);
            uiButton_HandToOpen.FillHoverColor = Color.FromArgb(133, 97, 198);
            uiButton_HandToOpen.FillPressColor = Color.FromArgb(82, 46, 147);
            uiButton_HandToOpen.FillSelectedColor = Color.FromArgb(82, 46, 147);
            uiButton_HandToOpen.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_HandToOpen.LightColor = Color.FromArgb(244, 242, 251);
            uiButton_HandToOpen.Location = new Point(1024, 2);
            uiButton_HandToOpen.MinimumSize = new Size(1, 1);
            uiButton_HandToOpen.Name = "uiButton_HandToOpen";
            uiButton_HandToOpen.RectColor = Color.FromArgb(102, 58, 183);
            uiButton_HandToOpen.RectHoverColor = Color.FromArgb(133, 97, 198);
            uiButton_HandToOpen.RectPressColor = Color.FromArgb(82, 46, 147);
            uiButton_HandToOpen.RectSelectedColor = Color.FromArgb(82, 46, 147);
            uiButton_HandToOpen.Size = new Size(85, 25);
            uiButton_HandToOpen.Style = Sunny.UI.UIStyle.Custom;
            uiButton_HandToOpen.TabIndex = 17;
            uiButton_HandToOpen.Text = "卡奖手动开盘";
            uiButton_HandToOpen.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_HandToOpen.Click += uiButton_HandToOpen_Click;
            // 
            // uiButton_CancelBetOrder
            // 
            uiButton_CancelBetOrder.Cursor = Cursors.Hand;
            uiButton_CancelBetOrder.FillColor = Color.FromArgb(220, 155, 40);
            uiButton_CancelBetOrder.FillColor2 = Color.FromArgb(220, 155, 40);
            uiButton_CancelBetOrder.FillHoverColor = Color.FromArgb(227, 175, 83);
            uiButton_CancelBetOrder.FillPressColor = Color.FromArgb(176, 124, 32);
            uiButton_CancelBetOrder.FillSelectedColor = Color.FromArgb(176, 124, 32);
            uiButton_CancelBetOrder.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_CancelBetOrder.LightColor = Color.FromArgb(253, 249, 241);
            uiButton_CancelBetOrder.Location = new Point(932, 2);
            uiButton_CancelBetOrder.MinimumSize = new Size(1, 1);
            uiButton_CancelBetOrder.Name = "uiButton_CancelBetOrder";
            uiButton_CancelBetOrder.RectColor = Color.FromArgb(220, 155, 40);
            uiButton_CancelBetOrder.RectHoverColor = Color.FromArgb(227, 175, 83);
            uiButton_CancelBetOrder.RectPressColor = Color.FromArgb(176, 124, 32);
            uiButton_CancelBetOrder.RectSelectedColor = Color.FromArgb(176, 124, 32);
            uiButton_CancelBetOrder.Size = new Size(85, 25);
            uiButton_CancelBetOrder.Style = Sunny.UI.UIStyle.Custom;
            uiButton_CancelBetOrder.TabIndex = 16;
            uiButton_CancelBetOrder.Text = "一键退回";
            uiButton_CancelBetOrder.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_CancelBetOrder.Click += uiButton_CancelBetOrder_Click;
            // 
            // uiButton_HandToBet
            // 
            uiButton_HandToBet.Cursor = Cursors.Hand;
            uiButton_HandToBet.Font = new Font("宋体", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            uiButton_HandToBet.Location = new Point(841, 2);
            uiButton_HandToBet.MinimumSize = new Size(1, 1);
            uiButton_HandToBet.Name = "uiButton_HandToBet";
            uiButton_HandToBet.Size = new Size(85, 25);
            uiButton_HandToBet.TabIndex = 15;
            uiButton_HandToBet.Text = "手动补投";
            uiButton_HandToBet.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            uiButton_HandToBet.Click += uiButton_HandToBet_Click;
            // 
            // checkBox_CancelOrder
            // 
            checkBox_CancelOrder.AutoSize = true;
            checkBox_CancelOrder.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_CancelOrder.Location = new Point(1134, 5);
            checkBox_CancelOrder.Name = "checkBox_CancelOrder";
            checkBox_CancelOrder.Size = new Size(99, 21);
            checkBox_CancelOrder.TabIndex = 14;
            checkBox_CancelOrder.Text = "失败自动退单";
            checkBox_CancelOrder.UseVisualStyleBackColor = true;
            checkBox_CancelOrder.CheckedChanged += checkBox_CancelOrder_CheckedChanged;
            // 
            // comboBox_BetLottery
            // 
            comboBox_BetLottery.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_BetLottery.FormattingEnabled = true;
            comboBox_BetLottery.Items.AddRange(new object[] { "全部" });
            comboBox_BetLottery.Location = new Point(735, 2);
            comboBox_BetLottery.Name = "comboBox_BetLottery";
            comboBox_BetLottery.Size = new Size(100, 25);
            comboBox_BetLottery.TabIndex = 13;
            // 
            // checkBox_IsDuiChong
            // 
            checkBox_IsDuiChong.AutoSize = true;
            checkBox_IsDuiChong.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_IsDuiChong.Location = new Point(621, 5);
            checkBox_IsDuiChong.Name = "checkBox_IsDuiChong";
            checkBox_IsDuiChong.Size = new Size(75, 21);
            checkBox_IsDuiChong.TabIndex = 7;
            checkBox_IsDuiChong.Text = "对冲吃单";
            checkBox_IsDuiChong.UseVisualStyleBackColor = true;
            // 
            // checkBox_IsFeiDan
            // 
            checkBox_IsFeiDan.AutoSize = true;
            checkBox_IsFeiDan.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_IsFeiDan.Location = new Point(539, 5);
            checkBox_IsFeiDan.Name = "checkBox_IsFeiDan";
            checkBox_IsFeiDan.Size = new Size(75, 21);
            checkBox_IsFeiDan.TabIndex = 6;
            checkBox_IsFeiDan.Text = "开启飞单";
            checkBox_IsFeiDan.UseVisualStyleBackColor = true;
            checkBox_IsFeiDan.CheckedChanged += checkBox_IsFeiDan_CheckedChanged;
            // 
            // comboBox_WorkGroupId
            // 
            comboBox_WorkGroupId.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_WorkGroupId.FormattingEnabled = true;
            comboBox_WorkGroupId.Location = new Point(4, 3);
            comboBox_WorkGroupId.Name = "comboBox_WorkGroupId";
            comboBox_WorkGroupId.Size = new Size(417, 25);
            comboBox_WorkGroupId.TabIndex = 2;
            // 
            // tabPage_BetOrderReport
            // 
            tabPage_BetOrderReport.Controls.Add(tableLayoutPanel2);
            tabPage_BetOrderReport.Location = new Point(4, 26);
            tabPage_BetOrderReport.Name = "tabPage_BetOrderReport";
            tabPage_BetOrderReport.Padding = new Padding(3);
            tabPage_BetOrderReport.Size = new Size(1275, 681);
            tabPage_BetOrderReport.TabIndex = 1;
            tabPage_BetOrderReport.Text = "▶投注明细";
            tabPage_BetOrderReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel2
            // 
            tableLayoutPanel2.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel2.ColumnCount = 1;
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Controls.Add(dataGridView_BetOrderReport, 0, 1);
            tableLayoutPanel2.Controls.Add(panel1, 0, 0);
            tableLayoutPanel2.Dock = DockStyle.Fill;
            tableLayoutPanel2.Location = new Point(3, 3);
            tableLayoutPanel2.Margin = new Padding(0);
            tableLayoutPanel2.Name = "tableLayoutPanel2";
            tableLayoutPanel2.RowCount = 2;
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Size = new Size(1269, 675);
            tableLayoutPanel2.TabIndex = 2;
            // 
            // dataGridView_BetOrderReport
            // 
            dataGridViewCellStyle16.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle16.BackColor = SystemColors.Control;
            dataGridViewCellStyle16.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle16.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle16.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle16.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle16.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle16;
            dataGridView_BetOrderReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle17.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle17.BackColor = SystemColors.Window;
            dataGridViewCellStyle17.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle17.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle17.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle17.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle17.WrapMode = DataGridViewTriState.False;
            dataGridView_BetOrderReport.DefaultCellStyle = dataGridViewCellStyle17;
            dataGridView_BetOrderReport.Dock = DockStyle.Fill;
            dataGridView_BetOrderReport.Location = new Point(4, 40);
            dataGridView_BetOrderReport.Name = "dataGridView_BetOrderReport";
            dataGridView_BetOrderReport.ReadOnly = true;
            dataGridViewCellStyle18.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle18.BackColor = SystemColors.Control;
            dataGridViewCellStyle18.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle18.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle18.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle18.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle18.WrapMode = DataGridViewTriState.True;
            dataGridView_BetOrderReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle18;
            dataGridView_BetOrderReport.Size = new Size(1261, 631);
            dataGridView_BetOrderReport.TabIndex = 1;
            dataGridView_BetOrderReport.VirtualMode = true;
            // 
            // panel1
            // 
            panel1.Controls.Add(comboBox_SelectMemberForBetOrder);
            panel1.Controls.Add(label4);
            panel1.Controls.Add(comboBox_SelectIssueForBetOrder);
            panel1.Controls.Add(label3);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(4, 4);
            panel1.Name = "panel1";
            panel1.Size = new Size(1261, 29);
            panel1.TabIndex = 0;
            // 
            // comboBox_SelectMemberForBetOrder
            // 
            comboBox_SelectMemberForBetOrder.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectMemberForBetOrder.FormattingEnabled = true;
            comboBox_SelectMemberForBetOrder.Location = new Point(89, 3);
            comboBox_SelectMemberForBetOrder.Name = "comboBox_SelectMemberForBetOrder";
            comboBox_SelectMemberForBetOrder.Size = new Size(250, 25);
            comboBox_SelectMemberForBetOrder.TabIndex = 3;
            comboBox_SelectMemberForBetOrder.SelectedIndexChanged += comboBox_SelectMemberForBetOrder_SelectedIndexChanged;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label4.ForeColor = Color.Red;
            label4.Location = new Point(4, 6);
            label4.Name = "label4";
            label4.Size = new Size(83, 19);
            label4.TabIndex = 2;
            label4.Text = "按会员查询:";
            // 
            // comboBox_SelectIssueForBetOrder
            // 
            comboBox_SelectIssueForBetOrder.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectIssueForBetOrder.FormattingEnabled = true;
            comboBox_SelectIssueForBetOrder.Location = new Point(431, 3);
            comboBox_SelectIssueForBetOrder.Name = "comboBox_SelectIssueForBetOrder";
            comboBox_SelectIssueForBetOrder.Size = new Size(170, 25);
            comboBox_SelectIssueForBetOrder.TabIndex = 1;
            comboBox_SelectIssueForBetOrder.SelectedIndexChanged += comboBox_SelectIssueForBetOrder_SelectedIndexChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label3.ForeColor = Color.Red;
            label3.Location = new Point(345, 6);
            label3.Name = "label3";
            label3.Size = new Size(83, 19);
            label3.TabIndex = 0;
            label3.Text = "按期号查询:";
            // 
            // tabPage_BetTotalReport
            // 
            tabPage_BetTotalReport.Controls.Add(tableLayoutPanel3);
            tabPage_BetTotalReport.Location = new Point(4, 26);
            tabPage_BetTotalReport.Name = "tabPage_BetTotalReport";
            tabPage_BetTotalReport.Padding = new Padding(3);
            tabPage_BetTotalReport.Size = new Size(1275, 681);
            tabPage_BetTotalReport.TabIndex = 2;
            tabPage_BetTotalReport.Text = "▶飞单汇总";
            tabPage_BetTotalReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel3
            // 
            tableLayoutPanel3.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel3.ColumnCount = 1;
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel3.Controls.Add(dataGridView_HuiZongReport, 0, 1);
            tableLayoutPanel3.Controls.Add(panel2, 0, 0);
            tableLayoutPanel3.Dock = DockStyle.Fill;
            tableLayoutPanel3.Location = new Point(3, 3);
            tableLayoutPanel3.Margin = new Padding(0);
            tableLayoutPanel3.Name = "tableLayoutPanel3";
            tableLayoutPanel3.RowCount = 2;
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel3.Size = new Size(1269, 675);
            tableLayoutPanel3.TabIndex = 3;
            // 
            // dataGridView_HuiZongReport
            // 
            dataGridViewCellStyle19.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle19.BackColor = SystemColors.Control;
            dataGridViewCellStyle19.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle19.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle19.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle19.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle19.WrapMode = DataGridViewTriState.True;
            dataGridView_HuiZongReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle19;
            dataGridView_HuiZongReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle20.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle20.BackColor = SystemColors.Window;
            dataGridViewCellStyle20.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle20.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle20.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle20.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle20.WrapMode = DataGridViewTriState.False;
            dataGridView_HuiZongReport.DefaultCellStyle = dataGridViewCellStyle20;
            dataGridView_HuiZongReport.Dock = DockStyle.Fill;
            dataGridView_HuiZongReport.Location = new Point(4, 40);
            dataGridView_HuiZongReport.Name = "dataGridView_HuiZongReport";
            dataGridView_HuiZongReport.ReadOnly = true;
            dataGridViewCellStyle21.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle21.BackColor = SystemColors.Control;
            dataGridViewCellStyle21.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle21.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle21.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle21.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle21.WrapMode = DataGridViewTriState.True;
            dataGridView_HuiZongReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle21;
            dataGridView_HuiZongReport.Size = new Size(1261, 631);
            dataGridView_HuiZongReport.TabIndex = 2;
            // 
            // panel2
            // 
            panel2.Controls.Add(comboBox_SelectIssueForHuiZong);
            panel2.Controls.Add(label6);
            panel2.Dock = DockStyle.Fill;
            panel2.Location = new Point(4, 4);
            panel2.Name = "panel2";
            panel2.Size = new Size(1261, 29);
            panel2.TabIndex = 1;
            // 
            // comboBox_SelectIssueForHuiZong
            // 
            comboBox_SelectIssueForHuiZong.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectIssueForHuiZong.FormattingEnabled = true;
            comboBox_SelectIssueForHuiZong.Location = new Point(77, 2);
            comboBox_SelectIssueForHuiZong.Name = "comboBox_SelectIssueForHuiZong";
            comboBox_SelectIssueForHuiZong.Size = new Size(250, 25);
            comboBox_SelectIssueForHuiZong.TabIndex = 1;
            comboBox_SelectIssueForHuiZong.SelectedIndexChanged += comboBox_SelectIssueForHuiZong_SelectedIndexChanged;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label6.ForeColor = Color.Red;
            label6.Location = new Point(2, 5);
            label6.Name = "label6";
            label6.Size = new Size(69, 19);
            label6.TabIndex = 0;
            label6.Text = "查询期号:";
            // 
            // tabPage_AddMoneyReport
            // 
            tabPage_AddMoneyReport.Controls.Add(tableLayoutPanel4);
            tabPage_AddMoneyReport.Location = new Point(4, 26);
            tabPage_AddMoneyReport.Name = "tabPage_AddMoneyReport";
            tabPage_AddMoneyReport.Size = new Size(1275, 681);
            tabPage_AddMoneyReport.TabIndex = 3;
            tabPage_AddMoneyReport.Text = "▶上分记录";
            tabPage_AddMoneyReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel4
            // 
            tableLayoutPanel4.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel4.ColumnCount = 1;
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel4.Controls.Add(dataGridView_AddMoneyReport, 0, 1);
            tableLayoutPanel4.Controls.Add(panel3, 0, 0);
            tableLayoutPanel4.Dock = DockStyle.Fill;
            tableLayoutPanel4.Location = new Point(0, 0);
            tableLayoutPanel4.Margin = new Padding(0);
            tableLayoutPanel4.Name = "tableLayoutPanel4";
            tableLayoutPanel4.RowCount = 2;
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel4.Size = new Size(1275, 681);
            tableLayoutPanel4.TabIndex = 3;
            // 
            // dataGridView_AddMoneyReport
            // 
            dataGridViewCellStyle22.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle22.BackColor = SystemColors.Control;
            dataGridViewCellStyle22.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle22.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle22.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle22.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle22.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoneyReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle22;
            dataGridView_AddMoneyReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle23.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle23.BackColor = SystemColors.Window;
            dataGridViewCellStyle23.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle23.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle23.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle23.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle23.WrapMode = DataGridViewTriState.False;
            dataGridView_AddMoneyReport.DefaultCellStyle = dataGridViewCellStyle23;
            dataGridView_AddMoneyReport.Dock = DockStyle.Fill;
            dataGridView_AddMoneyReport.Location = new Point(4, 40);
            dataGridView_AddMoneyReport.Name = "dataGridView_AddMoneyReport";
            dataGridView_AddMoneyReport.ReadOnly = true;
            dataGridViewCellStyle24.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle24.BackColor = SystemColors.Control;
            dataGridViewCellStyle24.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle24.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle24.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle24.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle24.WrapMode = DataGridViewTriState.True;
            dataGridView_AddMoneyReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle24;
            dataGridView_AddMoneyReport.Size = new Size(1267, 637);
            dataGridView_AddMoneyReport.TabIndex = 2;
            // 
            // panel3
            // 
            panel3.Controls.Add(comboBox_SelectObjForAddMoneyReport);
            panel3.Controls.Add(label7);
            panel3.Dock = DockStyle.Fill;
            panel3.Location = new Point(4, 4);
            panel3.Name = "panel3";
            panel3.Size = new Size(1267, 29);
            panel3.TabIndex = 1;
            // 
            // comboBox_SelectObjForAddMoneyReport
            // 
            comboBox_SelectObjForAddMoneyReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForAddMoneyReport.FormattingEnabled = true;
            comboBox_SelectObjForAddMoneyReport.Location = new Point(77, 2);
            comboBox_SelectObjForAddMoneyReport.Name = "comboBox_SelectObjForAddMoneyReport";
            comboBox_SelectObjForAddMoneyReport.Size = new Size(250, 25);
            comboBox_SelectObjForAddMoneyReport.TabIndex = 1;
            comboBox_SelectObjForAddMoneyReport.SelectedIndexChanged += comboBox_SelectObjForAddMoneyReport_SelectedIndexChanged;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label7.ForeColor = Color.Red;
            label7.Location = new Point(2, 5);
            label7.Name = "label7";
            label7.Size = new Size(69, 19);
            label7.TabIndex = 0;
            label7.Text = "查询会员:";
            // 
            // tabPage_SubMoneyReport
            // 
            tabPage_SubMoneyReport.Controls.Add(tableLayoutPanel5);
            tabPage_SubMoneyReport.Location = new Point(4, 26);
            tabPage_SubMoneyReport.Name = "tabPage_SubMoneyReport";
            tabPage_SubMoneyReport.Size = new Size(1275, 681);
            tabPage_SubMoneyReport.TabIndex = 4;
            tabPage_SubMoneyReport.Text = "▶下分记录";
            tabPage_SubMoneyReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel5
            // 
            tableLayoutPanel5.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel5.ColumnCount = 1;
            tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel5.Controls.Add(dataGridView_SubMoneyReport, 0, 1);
            tableLayoutPanel5.Controls.Add(panel4, 0, 0);
            tableLayoutPanel5.Dock = DockStyle.Fill;
            tableLayoutPanel5.Location = new Point(0, 0);
            tableLayoutPanel5.Margin = new Padding(0);
            tableLayoutPanel5.Name = "tableLayoutPanel5";
            tableLayoutPanel5.RowCount = 2;
            tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel5.Size = new Size(1275, 681);
            tableLayoutPanel5.TabIndex = 3;
            // 
            // dataGridView_SubMoneyReport
            // 
            dataGridViewCellStyle25.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle25.BackColor = SystemColors.Control;
            dataGridViewCellStyle25.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle25.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle25.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle25.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle25.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoneyReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle25;
            dataGridView_SubMoneyReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle26.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle26.BackColor = SystemColors.Window;
            dataGridViewCellStyle26.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle26.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle26.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle26.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle26.WrapMode = DataGridViewTriState.False;
            dataGridView_SubMoneyReport.DefaultCellStyle = dataGridViewCellStyle26;
            dataGridView_SubMoneyReport.Dock = DockStyle.Fill;
            dataGridView_SubMoneyReport.Location = new Point(4, 40);
            dataGridView_SubMoneyReport.Name = "dataGridView_SubMoneyReport";
            dataGridView_SubMoneyReport.ReadOnly = true;
            dataGridViewCellStyle27.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle27.BackColor = SystemColors.Control;
            dataGridViewCellStyle27.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle27.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle27.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle27.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle27.WrapMode = DataGridViewTriState.True;
            dataGridView_SubMoneyReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle27;
            dataGridView_SubMoneyReport.Size = new Size(1267, 637);
            dataGridView_SubMoneyReport.TabIndex = 2;
            // 
            // panel4
            // 
            panel4.Controls.Add(comboBox_SelectObjForSubMoneyReport);
            panel4.Controls.Add(label8);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(4, 4);
            panel4.Name = "panel4";
            panel4.Size = new Size(1267, 29);
            panel4.TabIndex = 1;
            // 
            // comboBox_SelectObjForSubMoneyReport
            // 
            comboBox_SelectObjForSubMoneyReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForSubMoneyReport.FormattingEnabled = true;
            comboBox_SelectObjForSubMoneyReport.Location = new Point(77, 2);
            comboBox_SelectObjForSubMoneyReport.Name = "comboBox_SelectObjForSubMoneyReport";
            comboBox_SelectObjForSubMoneyReport.Size = new Size(250, 25);
            comboBox_SelectObjForSubMoneyReport.TabIndex = 1;
            comboBox_SelectObjForSubMoneyReport.SelectedIndexChanged += comboBox_SelectObjForSubMoneyReport_SelectedIndexChanged;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label8.ForeColor = Color.Red;
            label8.Location = new Point(2, 5);
            label8.Name = "label8";
            label8.Size = new Size(69, 19);
            label8.TabIndex = 0;
            label8.Text = "查询会员:";
            // 
            // tabPage_FinanceReport
            // 
            tabPage_FinanceReport.Controls.Add(tableLayoutPanel6);
            tabPage_FinanceReport.Location = new Point(4, 26);
            tabPage_FinanceReport.Name = "tabPage_FinanceReport";
            tabPage_FinanceReport.Size = new Size(1275, 681);
            tabPage_FinanceReport.TabIndex = 5;
            tabPage_FinanceReport.Text = "▶财务记录";
            tabPage_FinanceReport.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel6
            // 
            tableLayoutPanel6.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel6.ColumnCount = 1;
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel6.Controls.Add(dataGridView_FinanceReport, 0, 1);
            tableLayoutPanel6.Controls.Add(panel5, 0, 0);
            tableLayoutPanel6.Dock = DockStyle.Fill;
            tableLayoutPanel6.Location = new Point(0, 0);
            tableLayoutPanel6.Margin = new Padding(0);
            tableLayoutPanel6.Name = "tableLayoutPanel6";
            tableLayoutPanel6.RowCount = 2;
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel6.Size = new Size(1275, 681);
            tableLayoutPanel6.TabIndex = 3;
            // 
            // dataGridView_FinanceReport
            // 
            dataGridViewCellStyle28.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle28.BackColor = SystemColors.Control;
            dataGridViewCellStyle28.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle28.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle28.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle28.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle28.WrapMode = DataGridViewTriState.True;
            dataGridView_FinanceReport.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle28;
            dataGridView_FinanceReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle29.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle29.BackColor = SystemColors.Window;
            dataGridViewCellStyle29.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle29.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle29.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle29.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle29.WrapMode = DataGridViewTriState.False;
            dataGridView_FinanceReport.DefaultCellStyle = dataGridViewCellStyle29;
            dataGridView_FinanceReport.Dock = DockStyle.Fill;
            dataGridView_FinanceReport.Location = new Point(4, 40);
            dataGridView_FinanceReport.Name = "dataGridView_FinanceReport";
            dataGridView_FinanceReport.ReadOnly = true;
            dataGridViewCellStyle30.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle30.BackColor = SystemColors.Control;
            dataGridViewCellStyle30.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle30.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle30.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle30.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle30.WrapMode = DataGridViewTriState.True;
            dataGridView_FinanceReport.RowHeadersDefaultCellStyle = dataGridViewCellStyle30;
            dataGridView_FinanceReport.Size = new Size(1267, 637);
            dataGridView_FinanceReport.TabIndex = 3;
            dataGridView_FinanceReport.VirtualMode = true;
            // 
            // panel5
            // 
            panel5.Controls.Add(comboBox_SelectObjForFinanceReport);
            panel5.Controls.Add(label5);
            panel5.Dock = DockStyle.Fill;
            panel5.Location = new Point(4, 4);
            panel5.Name = "panel5";
            panel5.Size = new Size(1267, 29);
            panel5.TabIndex = 2;
            // 
            // comboBox_SelectObjForFinanceReport
            // 
            comboBox_SelectObjForFinanceReport.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectObjForFinanceReport.FormattingEnabled = true;
            comboBox_SelectObjForFinanceReport.Location = new Point(77, 2);
            comboBox_SelectObjForFinanceReport.Name = "comboBox_SelectObjForFinanceReport";
            comboBox_SelectObjForFinanceReport.Size = new Size(250, 25);
            comboBox_SelectObjForFinanceReport.TabIndex = 1;
            comboBox_SelectObjForFinanceReport.SelectedIndexChanged += comboBox_SelectObjForFinanceReport_SelectedIndexChanged;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label5.ForeColor = Color.Red;
            label5.Location = new Point(2, 5);
            label5.Name = "label5";
            label5.Size = new Size(69, 19);
            label5.TabIndex = 0;
            label5.Text = "查询会员:";
            // 
            // tabPage_RecMsg
            // 
            tabPage_RecMsg.Controls.Add(tableLayoutPanel7);
            tabPage_RecMsg.Location = new Point(4, 26);
            tabPage_RecMsg.Name = "tabPage_RecMsg";
            tabPage_RecMsg.Size = new Size(1275, 681);
            tabPage_RecMsg.TabIndex = 6;
            tabPage_RecMsg.Text = "▶信息记录";
            tabPage_RecMsg.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel7
            // 
            tableLayoutPanel7.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel7.ColumnCount = 1;
            tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel7.Controls.Add(dataGridView_RecMsg, 0, 1);
            tableLayoutPanel7.Controls.Add(panel6, 0, 0);
            tableLayoutPanel7.Dock = DockStyle.Fill;
            tableLayoutPanel7.Location = new Point(0, 0);
            tableLayoutPanel7.Margin = new Padding(0);
            tableLayoutPanel7.Name = "tableLayoutPanel7";
            tableLayoutPanel7.RowCount = 2;
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel7.Size = new Size(1275, 681);
            tableLayoutPanel7.TabIndex = 3;
            // 
            // dataGridView_RecMsg
            // 
            dataGridViewCellStyle31.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle31.BackColor = SystemColors.Control;
            dataGridViewCellStyle31.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle31.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle31.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle31.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle31.WrapMode = DataGridViewTriState.True;
            dataGridView_RecMsg.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle31;
            dataGridView_RecMsg.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle32.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle32.BackColor = SystemColors.Window;
            dataGridViewCellStyle32.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle32.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle32.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle32.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle32.WrapMode = DataGridViewTriState.False;
            dataGridView_RecMsg.DefaultCellStyle = dataGridViewCellStyle32;
            dataGridView_RecMsg.Dock = DockStyle.Fill;
            dataGridView_RecMsg.Location = new Point(4, 40);
            dataGridView_RecMsg.Name = "dataGridView_RecMsg";
            dataGridView_RecMsg.ReadOnly = true;
            dataGridViewCellStyle33.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle33.BackColor = SystemColors.Control;
            dataGridViewCellStyle33.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle33.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle33.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle33.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle33.WrapMode = DataGridViewTriState.True;
            dataGridView_RecMsg.RowHeadersDefaultCellStyle = dataGridViewCellStyle33;
            dataGridView_RecMsg.Size = new Size(1267, 637);
            dataGridView_RecMsg.TabIndex = 3;
            dataGridView_RecMsg.VirtualMode = true;
            // 
            // panel6
            // 
            panel6.Controls.Add(comboBox_SelectMemberForShowRecMsg);
            panel6.Controls.Add(label9);
            panel6.Dock = DockStyle.Fill;
            panel6.Location = new Point(4, 4);
            panel6.Name = "panel6";
            panel6.Size = new Size(1267, 29);
            panel6.TabIndex = 2;
            // 
            // comboBox_SelectMemberForShowRecMsg
            // 
            comboBox_SelectMemberForShowRecMsg.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_SelectMemberForShowRecMsg.FormattingEnabled = true;
            comboBox_SelectMemberForShowRecMsg.Location = new Point(77, 2);
            comboBox_SelectMemberForShowRecMsg.Name = "comboBox_SelectMemberForShowRecMsg";
            comboBox_SelectMemberForShowRecMsg.Size = new Size(250, 25);
            comboBox_SelectMemberForShowRecMsg.TabIndex = 1;
            comboBox_SelectMemberForShowRecMsg.SelectedIndexChanged += comboBox_SelectMemberForShowRecMsg_SelectedIndexChanged;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label9.ForeColor = Color.Red;
            label9.Location = new Point(2, 5);
            label9.Name = "label9";
            label9.Size = new Size(69, 19);
            label9.TabIndex = 0;
            label9.Text = "查询会员:";
            // 
            // tabPage_Setting
            // 
            tabPage_Setting.Controls.Add(tableLayoutPanel8);
            tabPage_Setting.Location = new Point(4, 26);
            tabPage_Setting.Name = "tabPage_Setting";
            tabPage_Setting.Size = new Size(1275, 681);
            tabPage_Setting.TabIndex = 7;
            tabPage_Setting.Text = "▶设置";
            tabPage_Setting.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel8
            // 
            tableLayoutPanel8.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel8.ColumnCount = 2;
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 400F));
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel8.Controls.Add(tableLayoutPanel_Odds, 0, 0);
            tableLayoutPanel8.Controls.Add(panel_Setting, 1, 0);
            tableLayoutPanel8.Dock = DockStyle.Fill;
            tableLayoutPanel8.Location = new Point(0, 0);
            tableLayoutPanel8.Margin = new Padding(0);
            tableLayoutPanel8.Name = "tableLayoutPanel8";
            tableLayoutPanel8.RowCount = 1;
            tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel8.Size = new Size(1275, 681);
            tableLayoutPanel8.TabIndex = 4;
            // 
            // tableLayoutPanel_Odds
            // 
            tableLayoutPanel_Odds.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
            tableLayoutPanel_Odds.ColumnCount = 1;
            tableLayoutPanel_Odds.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Odds.Controls.Add(dataGridView_Odds, 0, 0);
            tableLayoutPanel_Odds.Controls.Add(button_SaveOdds, 0, 1);
            tableLayoutPanel_Odds.Dock = DockStyle.Fill;
            tableLayoutPanel_Odds.Location = new Point(4, 4);
            tableLayoutPanel_Odds.Name = "tableLayoutPanel_Odds";
            tableLayoutPanel_Odds.RowCount = 2;
            tableLayoutPanel_Odds.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Odds.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            tableLayoutPanel_Odds.Size = new Size(394, 673);
            tableLayoutPanel_Odds.TabIndex = 0;
            // 
            // dataGridView_Odds
            // 
            dataGridViewCellStyle34.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle34.BackColor = SystemColors.Control;
            dataGridViewCellStyle34.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle34.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle34.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle34.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle34.WrapMode = DataGridViewTriState.True;
            dataGridView_Odds.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle34;
            dataGridView_Odds.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle35.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle35.BackColor = SystemColors.Window;
            dataGridViewCellStyle35.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle35.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle35.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle35.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle35.WrapMode = DataGridViewTriState.False;
            dataGridView_Odds.DefaultCellStyle = dataGridViewCellStyle35;
            dataGridView_Odds.Dock = DockStyle.Fill;
            dataGridView_Odds.Location = new Point(4, 4);
            dataGridView_Odds.Name = "dataGridView_Odds";
            dataGridViewCellStyle36.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle36.BackColor = SystemColors.Control;
            dataGridViewCellStyle36.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle36.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle36.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle36.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle36.WrapMode = DataGridViewTriState.True;
            dataGridView_Odds.RowHeadersDefaultCellStyle = dataGridViewCellStyle36;
            dataGridView_Odds.Size = new Size(386, 604);
            dataGridView_Odds.TabIndex = 2;
            // 
            // button_SaveOdds
            // 
            button_SaveOdds.Dock = DockStyle.Fill;
            button_SaveOdds.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_SaveOdds.ForeColor = Color.Red;
            button_SaveOdds.Location = new Point(4, 615);
            button_SaveOdds.Name = "button_SaveOdds";
            button_SaveOdds.Size = new Size(386, 54);
            button_SaveOdds.TabIndex = 5;
            button_SaveOdds.Text = "保存";
            button_SaveOdds.UseVisualStyleBackColor = true;
            // 
            // panel_Setting
            // 
            panel_Setting.Controls.Add(button_ShowTime);
            panel_Setting.Controls.Add(groupBox8);
            panel_Setting.Controls.Add(groupBox6);
            panel_Setting.Controls.Add(groupBox5);
            panel_Setting.Controls.Add(groupBox3);
            panel_Setting.Controls.Add(groupBox2);
            panel_Setting.Controls.Add(groupBox1);
            panel_Setting.Dock = DockStyle.Fill;
            panel_Setting.Location = new Point(405, 4);
            panel_Setting.Name = "panel_Setting";
            panel_Setting.Size = new Size(866, 673);
            panel_Setting.TabIndex = 1;
            // 
            // button_ShowTime
            // 
            button_ShowTime.Location = new Point(13, 442);
            button_ShowTime.Name = "button_ShowTime";
            button_ShowTime.Size = new Size(314, 30);
            button_ShowTime.TabIndex = 2;
            button_ShowTime.Text = "查看标准网络时间";
            button_ShowTime.UseVisualStyleBackColor = true;
            button_ShowTime.Click += button_ShowTime_Click;
            // 
            // groupBox8
            // 
            groupBox8.Controls.Add(checkBox_AutoHuiShui);
            groupBox8.Controls.Add(checkBox_JiaRenAutoAddMoney);
            groupBox8.Location = new Point(13, 221);
            groupBox8.Name = "groupBox8";
            groupBox8.Size = new Size(314, 65);
            groupBox8.TabIndex = 12;
            groupBox8.TabStop = false;
            groupBox8.Text = "其它";
            // 
            // checkBox_AutoHuiShui
            // 
            checkBox_AutoHuiShui.AutoSize = true;
            checkBox_AutoHuiShui.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_AutoHuiShui.Location = new Point(71, 26);
            checkBox_AutoHuiShui.Name = "checkBox_AutoHuiShui";
            checkBox_AutoHuiShui.Size = new Size(75, 21);
            checkBox_AutoHuiShui.TabIndex = 10;
            checkBox_AutoHuiShui.Text = "自动回水";
            checkBox_AutoHuiShui.UseVisualStyleBackColor = true;
            checkBox_AutoHuiShui.CheckedChanged += checkBox_AutoHuiShui_CheckedChanged;
            // 
            // checkBox_JiaRenAutoAddMoney
            // 
            checkBox_JiaRenAutoAddMoney.AutoSize = true;
            checkBox_JiaRenAutoAddMoney.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            checkBox_JiaRenAutoAddMoney.Location = new Point(153, 26);
            checkBox_JiaRenAutoAddMoney.Name = "checkBox_JiaRenAutoAddMoney";
            checkBox_JiaRenAutoAddMoney.Size = new Size(99, 21);
            checkBox_JiaRenAutoAddMoney.TabIndex = 11;
            checkBox_JiaRenAutoAddMoney.Text = "假人自动上分";
            checkBox_JiaRenAutoAddMoney.UseVisualStyleBackColor = true;
            checkBox_JiaRenAutoAddMoney.CheckedChanged += checkBox_JiaRenAutoAddMoney_CheckedChanged;
            // 
            // groupBox6
            // 
            groupBox6.Controls.Add(label2);
            groupBox6.Controls.Add(comboBox_CloseTime);
            groupBox6.Location = new Point(13, 8);
            groupBox6.Name = "groupBox6";
            groupBox6.Size = new Size(314, 65);
            groupBox6.TabIndex = 8;
            groupBox6.TabStop = false;
            groupBox6.Text = "封盘时间";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label2.ForeColor = Color.Red;
            label2.Location = new Point(65, 29);
            label2.Name = "label2";
            label2.Size = new Size(41, 19);
            label2.TabIndex = 6;
            label2.Text = "封盘:";
            // 
            // comboBox_CloseTime
            // 
            comboBox_CloseTime.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_CloseTime.FormattingEnabled = true;
            comboBox_CloseTime.Location = new Point(111, 26);
            comboBox_CloseTime.Name = "comboBox_CloseTime";
            comboBox_CloseTime.Size = new Size(132, 25);
            comboBox_CloseTime.TabIndex = 7;
            // 
            // groupBox5
            // 
            groupBox5.Controls.Add(button_Clear);
            groupBox5.Controls.Add(checkBox_SaveMemberBalance);
            groupBox5.Controls.Add(checkBox_SaveMemberBaseInfo);
            groupBox5.Location = new Point(13, 292);
            groupBox5.Name = "groupBox5";
            groupBox5.Size = new Size(314, 65);
            groupBox5.TabIndex = 7;
            groupBox5.TabStop = false;
            groupBox5.Text = "清除数据 (基础信息包含备注名和回水比例)";
            // 
            // button_Clear
            // 
            button_Clear.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button_Clear.ForeColor = Color.Red;
            button_Clear.Location = new Point(199, 23);
            button_Clear.Name = "button_Clear";
            button_Clear.Size = new Size(100, 30);
            button_Clear.TabIndex = 4;
            button_Clear.Text = "清除数据";
            button_Clear.UseVisualStyleBackColor = true;
            // 
            // checkBox_SaveMemberBalance
            // 
            checkBox_SaveMemberBalance.AutoSize = true;
            checkBox_SaveMemberBalance.Location = new Point(115, 29);
            checkBox_SaveMemberBalance.Name = "checkBox_SaveMemberBalance";
            checkBox_SaveMemberBalance.Size = new Size(75, 21);
            checkBox_SaveMemberBalance.TabIndex = 3;
            checkBox_SaveMemberBalance.Text = "保留积分";
            checkBox_SaveMemberBalance.UseVisualStyleBackColor = true;
            // 
            // checkBox_SaveMemberBaseInfo
            // 
            checkBox_SaveMemberBaseInfo.AutoSize = true;
            checkBox_SaveMemberBaseInfo.Location = new Point(12, 29);
            checkBox_SaveMemberBaseInfo.Name = "checkBox_SaveMemberBaseInfo";
            checkBox_SaveMemberBaseInfo.Size = new Size(99, 21);
            checkBox_SaveMemberBaseInfo.TabIndex = 2;
            checkBox_SaveMemberBaseInfo.Text = "保留基础信息";
            checkBox_SaveMemberBaseInfo.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(radioButton_ImgType2);
            groupBox3.Controls.Add(radioButton_ImgType1);
            groupBox3.Controls.Add(checkBox_6Rows);
            groupBox3.Controls.Add(checkBox_7Rows);
            groupBox3.Location = new Point(13, 150);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(314, 65);
            groupBox3.TabIndex = 5;
            groupBox3.TabStop = false;
            groupBox3.Text = "路子图";
            // 
            // radioButton_ImgType2
            // 
            radioButton_ImgType2.AutoSize = true;
            radioButton_ImgType2.Location = new Point(236, 27);
            radioButton_ImgType2.Name = "radioButton_ImgType2";
            radioButton_ImgType2.Size = new Size(57, 21);
            radioButton_ImgType2.TabIndex = 3;
            radioButton_ImgType2.TabStop = true;
            radioButton_ImgType2.Text = "样式2";
            radioButton_ImgType2.UseVisualStyleBackColor = true;
            radioButton_ImgType2.CheckedChanged += radioButton_ImgType2_CheckedChanged;
            // 
            // radioButton_ImgType1
            // 
            radioButton_ImgType1.AutoSize = true;
            radioButton_ImgType1.Location = new Point(165, 27);
            radioButton_ImgType1.Name = "radioButton_ImgType1";
            radioButton_ImgType1.Size = new Size(57, 21);
            radioButton_ImgType1.TabIndex = 2;
            radioButton_ImgType1.TabStop = true;
            radioButton_ImgType1.Text = "样式1";
            radioButton_ImgType1.UseVisualStyleBackColor = true;
            radioButton_ImgType1.CheckedChanged += radioButton_ImgType1_CheckedChanged;
            // 
            // checkBox_6Rows
            // 
            checkBox_6Rows.AutoSize = true;
            checkBox_6Rows.Location = new Point(95, 29);
            checkBox_6Rows.Name = "checkBox_6Rows";
            checkBox_6Rows.Size = new Size(46, 21);
            checkBox_6Rows.TabIndex = 1;
            checkBox_6Rows.Text = "6行";
            checkBox_6Rows.UseVisualStyleBackColor = true;
            // 
            // checkBox_7Rows
            // 
            checkBox_7Rows.AutoSize = true;
            checkBox_7Rows.Location = new Point(33, 29);
            checkBox_7Rows.Name = "checkBox_7Rows";
            checkBox_7Rows.Size = new Size(46, 21);
            checkBox_7Rows.TabIndex = 0;
            checkBox_7Rows.Text = "7行";
            checkBox_7Rows.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(button_CancelBetData);
            groupBox2.Controls.Add(textBox_CancelIssue);
            groupBox2.Location = new Point(13, 363);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(314, 65);
            groupBox2.TabIndex = 4;
            groupBox2.TabStop = false;
            groupBox2.Text = "撤销期号答题 (默认最后一期)";
            // 
            // button_CancelBetData
            // 
            button_CancelBetData.Location = new Point(199, 23);
            button_CancelBetData.Name = "button_CancelBetData";
            button_CancelBetData.Size = new Size(100, 30);
            button_CancelBetData.TabIndex = 1;
            button_CancelBetData.Text = "确定";
            button_CancelBetData.UseVisualStyleBackColor = true;
            // 
            // textBox_CancelIssue
            // 
            textBox_CancelIssue.BorderStyle = BorderStyle.FixedSingle;
            textBox_CancelIssue.Location = new Point(18, 27);
            textBox_CancelIssue.Name = "textBox_CancelIssue";
            textBox_CancelIssue.Size = new Size(150, 23);
            textBox_CancelIssue.TabIndex = 0;
            textBox_CancelIssue.TextAlign = HorizontalAlignment.Center;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(button_OneKeyRebate);
            groupBox1.Controls.Add(checkBox_ShowReturnCommissionDetail);
            groupBox1.Controls.Add(button_SaveReturnCommissionPercent);
            groupBox1.Controls.Add(textBox_ReturnCommissionPercent);
            groupBox1.Location = new Point(13, 79);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(314, 65);
            groupBox1.TabIndex = 3;
            groupBox1.TabStop = false;
            groupBox1.Text = "回水比例";
            // 
            // button_OneKeyRebate
            // 
            button_OneKeyRebate.Location = new Point(198, 37);
            button_OneKeyRebate.Name = "button_OneKeyRebate";
            button_OneKeyRebate.Size = new Size(100, 23);
            button_OneKeyRebate.TabIndex = 2;
            button_OneKeyRebate.Text = "一键回水";
            button_OneKeyRebate.UseVisualStyleBackColor = true;
            // 
            // checkBox_ShowReturnCommissionDetail
            // 
            checkBox_ShowReturnCommissionDetail.AutoSize = true;
            checkBox_ShowReturnCommissionDetail.Location = new Point(198, 14);
            checkBox_ShowReturnCommissionDetail.Name = "checkBox_ShowReturnCommissionDetail";
            checkBox_ShowReturnCommissionDetail.Size = new Size(99, 21);
            checkBox_ShowReturnCommissionDetail.TabIndex = 2;
            checkBox_ShowReturnCommissionDetail.Text = "显示回水金额";
            checkBox_ShowReturnCommissionDetail.UseVisualStyleBackColor = true;
            checkBox_ShowReturnCommissionDetail.CheckedChanged += checkBox_ShowReturnCommissionDetail_CheckedChanged;
            // 
            // button_SaveReturnCommissionPercent
            // 
            button_SaveReturnCommissionPercent.Location = new Point(95, 25);
            button_SaveReturnCommissionPercent.Name = "button_SaveReturnCommissionPercent";
            button_SaveReturnCommissionPercent.Size = new Size(80, 30);
            button_SaveReturnCommissionPercent.TabIndex = 1;
            button_SaveReturnCommissionPercent.Text = "确定";
            button_SaveReturnCommissionPercent.UseVisualStyleBackColor = true;
            // 
            // textBox_ReturnCommissionPercent
            // 
            textBox_ReturnCommissionPercent.BorderStyle = BorderStyle.FixedSingle;
            textBox_ReturnCommissionPercent.Location = new Point(18, 29);
            textBox_ReturnCommissionPercent.Name = "textBox_ReturnCommissionPercent";
            textBox_ReturnCommissionPercent.Size = new Size(71, 23);
            textBox_ReturnCommissionPercent.TabIndex = 0;
            textBox_ReturnCommissionPercent.TextAlign = HorizontalAlignment.Center;
            // 
            // tabPage_Log
            // 
            tabPage_Log.Controls.Add(dataGridView_Log);
            tabPage_Log.Location = new Point(4, 26);
            tabPage_Log.Name = "tabPage_Log";
            tabPage_Log.Padding = new Padding(3);
            tabPage_Log.Size = new Size(1275, 681);
            tabPage_Log.TabIndex = 9;
            tabPage_Log.Text = "▶日志";
            tabPage_Log.UseVisualStyleBackColor = true;
            // 
            // dataGridView_Log
            // 
            dataGridView_Log.AllowUserToAddRows = false;
            dataGridView_Log.AllowUserToDeleteRows = false;
            dataGridViewCellStyle37.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle37.BackColor = SystemColors.Control;
            dataGridViewCellStyle37.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle37.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle37.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle37.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle37.WrapMode = DataGridViewTriState.True;
            dataGridView_Log.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle37;
            dataGridView_Log.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView_Log.Columns.AddRange(new DataGridViewColumn[] { Log_Id, Log_Type, Log_Time, Log_Title, Log_Content });
            dataGridViewCellStyle38.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle38.BackColor = SystemColors.Window;
            dataGridViewCellStyle38.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle38.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle38.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle38.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle38.WrapMode = DataGridViewTriState.False;
            dataGridView_Log.DefaultCellStyle = dataGridViewCellStyle38;
            dataGridView_Log.Dock = DockStyle.Fill;
            dataGridView_Log.Location = new Point(3, 3);
            dataGridView_Log.Name = "dataGridView_Log";
            dataGridView_Log.ReadOnly = true;
            dataGridViewCellStyle39.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle39.BackColor = SystemColors.Control;
            dataGridViewCellStyle39.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle39.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle39.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle39.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle39.WrapMode = DataGridViewTriState.True;
            dataGridView_Log.RowHeadersDefaultCellStyle = dataGridViewCellStyle39;
            dataGridView_Log.Size = new Size(1269, 675);
            dataGridView_Log.TabIndex = 1;
            dataGridView_Log.VirtualMode = true;
            // 
            // Log_Id
            // 
            Log_Id.HeaderText = "序号";
            Log_Id.Name = "Log_Id";
            Log_Id.ReadOnly = true;
            Log_Id.Width = 60;
            // 
            // Log_Type
            // 
            Log_Type.HeaderText = "类型";
            Log_Type.Name = "Log_Type";
            Log_Type.ReadOnly = true;
            Log_Type.Width = 80;
            // 
            // Log_Time
            // 
            Log_Time.HeaderText = "时间";
            Log_Time.Name = "Log_Time";
            Log_Time.ReadOnly = true;
            Log_Time.Width = 130;
            // 
            // Log_Title
            // 
            Log_Title.HeaderText = "摘要";
            Log_Title.Name = "Log_Title";
            Log_Title.ReadOnly = true;
            Log_Title.Width = 200;
            // 
            // Log_Content
            // 
            Log_Content.HeaderText = "详情";
            Log_Content.Name = "Log_Content";
            Log_Content.ReadOnly = true;
            Log_Content.Width = 800;
            // 
            // tabPage_OneChat
            // 
            tabPage_OneChat.Controls.Add(webView2_OneChat);
            tabPage_OneChat.Location = new Point(4, 26);
            tabPage_OneChat.Name = "tabPage_OneChat";
            tabPage_OneChat.Padding = new Padding(3);
            tabPage_OneChat.Size = new Size(1275, 681);
            tabPage_OneChat.TabIndex = 10;
            tabPage_OneChat.Text = "⬛⬛⬛【一起聊吧】";
            tabPage_OneChat.UseVisualStyleBackColor = true;
            // 
            // webView2_OneChat
            // 
            webView2_OneChat.AllowExternalDrop = true;
            webView2_OneChat.CreationProperties = null;
            webView2_OneChat.DefaultBackgroundColor = Color.White;
            webView2_OneChat.Dock = DockStyle.Fill;
            webView2_OneChat.Location = new Point(3, 3);
            webView2_OneChat.Name = "webView2_OneChat";
            webView2_OneChat.Size = new Size(1269, 675);
            webView2_OneChat.TabIndex = 0;
            webView2_OneChat.ZoomFactor = 1D;
            // 
            // contextMenuStrip_Menu
            // 
            contextMenuStrip_Menu.Name = "contextMenuStrip_Menu";
            contextMenuStrip_Menu.Size = new Size(61, 4);
            // 
            // notifyIcon_Pass
            // 
            notifyIcon_Pass.Text = "notifyIcon1";
            notifyIcon_Pass.Visible = true;
            // 
            // notifyIcon_NG
            // 
            notifyIcon_NG.Text = "notifyIcon1";
            notifyIcon_NG.Visible = true;
            // 
            // FormMain
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1283, 737);
            Controls.Add(tabControl_Main);
            Controls.Add(statusStrip_Foot);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Name = "FormMain";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Robot";
            WindowState = FormWindowState.Maximized;
            FormClosing += FormMain_FormClosing;
            Load += FormMain_Load;
            statusStrip_Foot.ResumeLayout(false);
            statusStrip_Foot.PerformLayout();
            tabControl_Main.ResumeLayout(false);
            tabPage_Home.ResumeLayout(false);
            tableLayoutPanel_Main.ResumeLayout(false);
            tableLayoutPanel_Body.ResumeLayout(false);
            tableLayoutPanel1.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_PlatformLog).EndInit();
            ((ISupportInitialize)dataGridView_SubMoney).EndInit();
            ((ISupportInitialize)dataGridView_AddMoney).EndInit();
            tableLayoutPanel_MemberAndBetOrder.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_BetOrderDetail).EndInit();
            ((ISupportInitialize)dataGridView_MemberInfo).EndInit();
            panel_HomeMenu.ResumeLayout(false);
            panel_HomeMenu.PerformLayout();
            tabPage_BetOrderReport.ResumeLayout(false);
            tableLayoutPanel2.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_BetOrderReport).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            tabPage_BetTotalReport.ResumeLayout(false);
            tableLayoutPanel3.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_HuiZongReport).EndInit();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            tabPage_AddMoneyReport.ResumeLayout(false);
            tableLayoutPanel4.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_AddMoneyReport).EndInit();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            tabPage_SubMoneyReport.ResumeLayout(false);
            tableLayoutPanel5.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_SubMoneyReport).EndInit();
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            tabPage_FinanceReport.ResumeLayout(false);
            tableLayoutPanel6.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_FinanceReport).EndInit();
            panel5.ResumeLayout(false);
            panel5.PerformLayout();
            tabPage_RecMsg.ResumeLayout(false);
            tableLayoutPanel7.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_RecMsg).EndInit();
            panel6.ResumeLayout(false);
            panel6.PerformLayout();
            tabPage_Setting.ResumeLayout(false);
            tableLayoutPanel8.ResumeLayout(false);
            tableLayoutPanel_Odds.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_Odds).EndInit();
            panel_Setting.ResumeLayout(false);
            groupBox8.ResumeLayout(false);
            groupBox8.PerformLayout();
            groupBox6.ResumeLayout(false);
            groupBox6.PerformLayout();
            groupBox5.ResumeLayout(false);
            groupBox5.PerformLayout();
            groupBox3.ResumeLayout(false);
            groupBox3.PerformLayout();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            tabPage_Log.ResumeLayout(false);
            ((ISupportInitialize)dataGridView_Log).EndInit();
            tabPage_OneChat.ResumeLayout(false);
            ((ISupportInitialize)webView2_OneChat).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        private Sunny.UI.UIButton uiButton_Service;

        private Sunny.UI.UIButton uiButton_HandToOpen;

        private Sunny.UI.UIButton uiButton_CancelBetOrder;

        private Sunny.UI.UIButton uiButton_HandToBet;

        private System.Windows.Forms.CheckBox checkBox_CancelOrder;

        #endregion

        private StatusStrip statusStrip_Foot;
        private System.Windows.Forms.TabControl tabControl_Main;
        private TabPage tabPage_Home;
        private TabPage tabPage_BetOrderReport;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Main;
        private TabPage tabPage_BetTotalReport;
        private TabPage tabPage_AddMoneyReport;
        private TabPage tabPage_SubMoneyReport;
        private TabPage tabPage_FinanceReport;
        private TabPage tabPage_RecMsg;
        private TabPage tabPage_Setting;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Body;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_MemberAndBetOrder;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.DataGridView dataGridView_PlatformLog;
        private System.Windows.Forms.DataGridView dataGridView_SubMoney;
        private System.Windows.Forms.DataGridView dataGridView_AddMoney;
        private System.Windows.Forms.DataGridView dataGridView_BetOrderDetail;
        private System.Windows.Forms.DataGridView dataGridView_MemberInfo;
        private TableLayoutPanel tableLayoutPanel2;
        private TableLayoutPanel tableLayoutPanel3;
        private TableLayoutPanel tableLayoutPanel4;
        private TableLayoutPanel tableLayoutPanel5;
        private TableLayoutPanel tableLayoutPanel6;
        private TableLayoutPanel tableLayoutPanel7;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel8;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Odds;
        private System.Windows.Forms.DataGridView dataGridView_Odds;
        private System.Windows.Forms.Panel panel_Setting;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Panel panel_HomeMenu;
        private System.Windows.Forms.ComboBox comboBox_WorkGroupId;
        private System.Windows.Forms.Button button_SaveReturnCommissionPercent;
        private System.Windows.Forms.TextBox textBox_ReturnCommissionPercent;
        private System.Windows.Forms.GroupBox groupBox2;
        private Button button_CancelBetData;
        private TextBox textBox_CancelIssue;
        private System.Windows.Forms.GroupBox groupBox3;
        private CheckBox checkBox_6Rows;
        private CheckBox checkBox_7Rows;
        private Button button_OneKeyRebate;
        private System.Windows.Forms.GroupBox groupBox5;
        private CheckBox checkBox_SaveMemberBalance;
        private CheckBox checkBox_SaveMemberBaseInfo;
        private Button button_Clear;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Button button_SaveOdds;
        private Panel panel1;
        private Label label3;
        private ComboBox comboBox_SelectIssueForBetOrder;
        private ComboBox comboBox_SelectMemberForBetOrder;
        private Label label4;
        private Panel panel2;
        private ComboBox comboBox_SelectIssueForHuiZong;
        private Label label6;
        private Panel panel3;
        private ComboBox comboBox_SelectObjForAddMoneyReport;
        private Label label7;
        private Panel panel4;
        private ComboBox comboBox_SelectObjForSubMoneyReport;
        private Label label8;
        private Panel panel5;
        private ComboBox comboBox_SelectObjForFinanceReport;
        private Label label5;
        private Panel panel6;
        private ComboBox comboBox_SelectMemberForShowRecMsg;
        private Label label9;
        private TabPage tabPage_Log;
        private DataGridView dataGridView_BetOrderReport;
        private DataGridView dataGridView_HuiZongReport;
        private DataGridView dataGridView_AddMoneyReport;
        private DataGridView dataGridView_SubMoneyReport;
        private DataGridView dataGridView_FinanceReport;
        private DataGridView dataGridView_RecMsg;
        private System.Windows.Forms.DataGridView dataGridView_Log;
        private ContextMenuStrip contextMenuStrip_Menu;
        private DataGridViewTextBoxColumn PlatformLog_Id;
        private DataGridViewTextBoxColumn PlatformLog_Time;
        private DataGridViewTextBoxColumn PlatformLog_Title;
        private DataGridViewTextBoxColumn PlatformLog_Content;
        private ToolStripStatusLabel toolStripStatusLabel_CloseTime;
        private ToolStripStatusLabel toolStripStatusLabel_OpenTime;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel_MemberInfo;
        private ToolStripStatusLabel toolStripStatusLabel_Lottery;
        private ToolStripStatusLabel toolStripStatusLabel_RobotInfo;
        private DataGridViewTextBoxColumn SubMoney_Id;
        private DataGridViewTextBoxColumn SubMoney_Account;
        private DataGridViewTextBoxColumn SubMoney_RemarkName;
        private DataGridViewTextBoxColumn SubMoney_Money;
        private DataGridViewButtonColumn SubMoney_Agree;
        private DataGridViewButtonColumn SubMoney_Refuse;
        private DataGridViewTextBoxColumn AddMoney_Id;
        private DataGridViewTextBoxColumn AddMoney_Account;
        private DataGridViewTextBoxColumn AddMoney_RemarkName;
        private DataGridViewTextBoxColumn AddMoney_Money;
        private DataGridViewButtonColumn AddMoney_Agree;
        private DataGridViewButtonColumn AddMoney_Refuse;
        private System.Windows.Forms.CheckBox checkBox_IsDuiChong;
        private System.Windows.Forms.CheckBox checkBox_IsFeiDan;
        private System.Windows.Forms.CheckBox checkBox_JiaRenAutoAddMoney;
        private System.Windows.Forms.CheckBox checkBox_AutoHuiShui;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox comboBox_CloseTime;
        private DataGridViewTextBoxColumn Log_Id;
        private DataGridViewTextBoxColumn Log_Type;
        private DataGridViewTextBoxColumn Log_Time;
        private DataGridViewTextBoxColumn Log_Title;
        private DataGridViewTextBoxColumn Log_Content;
        private NotifyIcon notifyIcon_Pass;
        private System.Windows.Forms.NotifyIcon notifyIcon_NG;
        private DataGridViewTextBoxColumn BetOrderDetail_Account;
        private DataGridViewTextBoxColumn BetOrderDetail_RemarkName;
        private DataGridViewTextBoxColumn BetOrderDetail_BetLottery;
        private DataGridViewTextBoxColumn BetOrderDetail_Issue;
        private DataGridViewTextBoxColumn BetOrderDetail_Content;
        private DataGridViewTextBoxColumn BetOrderDetail_Odds;
        private DataGridViewTextBoxColumn BetOrderDetail_Balance;
        private System.Windows.Forms.ComboBox comboBox_BetLottery;
        private System.Windows.Forms.CheckBox checkBox_ShowReturnCommissionDetail;
        private Button button_ShowTime;
        private GroupBox groupBox8;
        private RadioButton radioButton_ImgType1;
        private RadioButton radioButton_ImgType2;
        private TabPage tabPage_OneChat;
        private Microsoft.Web.WebView2.WinForms.WebView2 webView2_OneChat;
    }
}
