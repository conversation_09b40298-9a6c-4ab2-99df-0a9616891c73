﻿using AiHelper;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

/// <summary>
/// 报表
/// </summary>
public partial class FormMain
{
    #region 处理上分订单

    /// <summary>
    /// 上分表格CellClick事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_AddMoney_CellClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex >= 0 && (e.ColumnIndex == 4 || e.ColumnIndex == 5))
            {
                // 引用表格
                DataGridView dgv = dataGridView_AddMoney;

                // 取出申请单ID
                long orderId = (long)dgv.Rows[e.RowIndex].Cells["AddMoney_Id"].Value;

                // 删除行
                dgv.Rows.RemoveAt(e.RowIndex);

                // 从数据库中获取申请单信息
                AddMoney am = await DbHelper.FSql.Select<AddMoney>()
                    .Where(t => t.Id.Equals(orderId))
                    .ToOneAsync();

                // 判断类型
                if (e.ColumnIndex == dataGridView_AddMoney.Columns.Count - 2)
                {
                    await AddMoneyHelper.AddMoneyHandlerAgree(am);
                }
                else if (e.ColumnIndex == dataGridView_AddMoney.Columns.Count - 1)
                {
                    await AddMoneyHelper.AddMoneyHandlerRefuse(am);
                }

                CommonHelper.NeedToRefreshMemberInfo = true;
            }
        }
        catch (Exception ex)
        {
            // Debug.WriteLine(ex.ToString());
            await DbHelper.AddLog(EnumLogType.机器人, "dataGridView_AddMoney_CellClick", ex.ToString());
        }
    }

    /// <summary>
    /// 处理假人自动上分
    /// </summary>
    private async Task JiaRenAutoAddMoneyHandler()
    {
        await Invoke(async () =>
        {
            try
            {
                // 引用表格
                DataGridView dgv = dataGridView_AddMoney;

                // 遍历每行数据
                for (int i = dgv.Rows.Count - 1; i >= 0; i--)
                {
                    // 取出申请单信息
                    string account = dgv.Rows[i].Cells["AddMoney_Account"].Value.ToString()!;

                    // 获取会员信息
                    Member member = await DbHelper.GetMember(account);
                    if (!member.是否假人)
                    {
                        continue;
                    }

                    // 取出申请单ID
                    long orderId = (long)dgv.Rows[i].Cells["AddMoney_Id"].Value;

                    // 删除行
                    dgv.Rows.RemoveAt(i);

                    // 从数据库中获取申请单信息
                    AddMoney am = await DbHelper.FSql.Select<AddMoney>()
                        .Where(t => t.Id.Equals(orderId))
                        .ToOneAsync();

                    // 处理上分
                    await AddMoneyHelper.AddMoneyHandlerAgree(am);
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "JiaRenAutoAddMoneyHandlerError", ex.ToString());
            }
        });
    }

    #endregion

    #region 处理下分订单

    /// <summary>
    /// 下分表格CellClick事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_SubMoney_CellClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex >= 0 && (e.ColumnIndex == 4 || e.ColumnIndex == 5))
            {
                // 引用表格
                DataGridView dgv = dataGridView_SubMoney;

                // 取出申请单ID
                long oderId = (long)dataGridView_SubMoney.Rows[e.RowIndex].Cells["SubMoney_Id"].Value;

                // 删除行
                dgv.Rows.RemoveAt(e.RowIndex);

                // 从数据库中获取申请单信息
                SubMoney sm = await DbHelper.FSql.Select<SubMoney>()
                    .Where(t => t.Id.Equals(oderId))
                    .ToOneAsync();

                // 修改申请单状态
                if (e.ColumnIndex.Equals(4)) // 同意
                {
                    await SubMoneyHelper.SubMoneyHandlerAgree(sm);
                }
                else if (e.ColumnIndex == 5) // 拒绝
                {
                    await SubMoneyHelper.SubMoneyHandlerRefuse(sm);
                }

                CommonHelper.NeedToRefreshMemberInfo = true;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "dataGridView_SubMoney_CellClickError", ex.ToString());
        }
    }

    #endregion

    #region 答题报表

    private int SelectBetOrderReportModel { get; set; } = 1;

    /// <summary>
    /// 按会员查询答题报表
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void comboBox_SelectMemberForBetOrder_SelectedIndexChanged(object? sender, EventArgs e)
    {
        try
        {
            SelectBetOrderReportModel = 1;
            ShowBetOrderReport();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectMemberForBetOrder_SelectedIndexChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 按期号查询答题报表
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void comboBox_SelectIssueForBetOrder_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            SelectBetOrderReportModel = 2;
            ShowBetOrderReport();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectIssueForBetOrder_SelectedIndexChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 显示答题报表
    /// </summary>
    private async void ShowBetOrderReport()
    {
        try
        {
            DataGridView dgv = dataGridView_BetOrderReport;
            dgv.Visible = false;
            dgv.DataSource = null;

            if (SelectBetOrderReportModel.Equals(1))
            {
                // 查询全部
                if (comboBox_SelectMemberForBetOrder.SelectedIndex.Equals(0))
                {
                    var list = DbHelper.FSql.Select<BetOrder, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .ToList(t => new
                        {
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            时间 = t.t1.Time.ToString("HH:mm:ss"),
                            游戏 = t.t1.BetLottery,
                            期号 = t.t1.Issue,
                            项目 = t.t1.Con,
                            赔率 = t.t1.Odds,
                            金额 = t.t1.Money,
                            开奖 = t.t1.DrawResult,
                            中挂 = t.t1.WinLose,
                            结算 = Math.Round(t.t1.结算, 2),
                            回水状态 = t.t1.BetOrderRebateStatus,
                            t.t1.回水金额,
                            处理状态 = t.t1.BetOrderStatus,
                            对应消息Id = t.t1.FromMsgId,
                            真假投注 = t.t1.OrderType
                        });

                    dgv.DataSource = list;
                }
                // 查询指定会员
                else
                {
                    string account = Ai.GetTextMiddle(comboBox_SelectMemberForBetOrder.Text.Trim(), Ai.中括号左, Ai.中括号右);
                    var list = DbHelper.FSql.Select<BetOrder, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .Where(t => t.t1.Account.Equals(account))
                        .ToList(t => new
                        {
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            时间 = t.t1.Time.ToString("HH:mm:ss"),
                            游戏 = t.t1.BetLottery,
                            期号 = t.t1.Issue,
                            项目 = t.t1.Con,
                            赔率 = t.t1.Odds,
                            金额 = t.t1.Money,
                            开奖 = t.t1.DrawResult,
                            中挂 = t.t1.WinLose,
                            结算 = Math.Round(t.t1.结算, 2),
                            回水状态 = t.t1.BetOrderRebateStatus,
                            t.t1.回水金额,
                            处理状态 = t.t1.BetOrderStatus,
                            对应消息Id = t.t1.FromMsgId,
                            真假投注 = t.t1.OrderType
                        });

                    // 绑定数据
                    dgv.DataSource = list;
                }
            }
            else if (SelectBetOrderReportModel.Equals(2))
            {
                // 查询全部
                if (comboBox_SelectIssueForBetOrder.SelectedIndex.Equals(0))
                {
                    var list = DbHelper.FSql.Select<BetOrder, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .ToList(t => new
                        {
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            时间 = t.t1.Time.ToString("HH:mm:ss"),
                            游戏 = t.t1.BetLottery,
                            期号 = t.t1.Issue,
                            项目 = t.t1.Con,
                            赔率 = t.t1.Odds,
                            金额 = t.t1.Money,
                            开奖 = t.t1.DrawResult,
                            中挂 = t.t1.WinLose,
                            结算 = Math.Round(t.t1.结算, 2),
                            回水状态 = t.t1.BetOrderRebateStatus,
                            t.t1.回水金额,
                            处理状态 = t.t1.BetOrderStatus,
                            对应消息Id = t.t1.FromMsgId,
                            真假投注 = t.t1.OrderType
                        });

                    dgv.DataSource = list;
                }
                // 查询指定期号
                else
                {
                    string issue = comboBox_SelectIssueForBetOrder.Text.Trim();
                    var list = DbHelper.FSql.Select<BetOrder, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .Where(t => t.t1.Issue.Equals(issue))
                        .ToList(t => new
                        {
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            时间 = t.t1.Time.ToString("HH:mm:ss"),
                            游戏 = t.t1.BetLottery,
                            期号 = t.t1.Issue,
                            项目 = t.t1.Con,
                            赔率 = t.t1.Odds,
                            金额 = t.t1.Money,
                            开奖 = t.t1.DrawResult,
                            中挂 = t.t1.WinLose,
                            结算 = Math.Round(t.t1.结算, 2),
                            回水状态 = t.t1.BetOrderRebateStatus,
                            t.t1.回水金额,
                            处理状态 = t.t1.BetOrderStatus,
                            对应消息Id = t.t1.FromMsgId,
                            真假投注 = t.t1.OrderType
                        });

                    dgv.DataSource = list;
                }
            }

            if (dgv.Rows.Count > 0)
            {
                // 设置列宽
                dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                // 设置列对齐方式
                dgv.Columns["真假投注"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["对应消息Id"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["处理状态"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["回水金额"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["回水状态"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["结算"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["中挂"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["开奖"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["金额"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["赔率"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["项目"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["期号"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["游戏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["备注名"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["账号"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                // 设置第1行的背景颜色为绿色，字体为白色
                dgv.Rows[0].DefaultCellStyle.BackColor = Color.Green;
                dgv.Rows[0].DefaultCellStyle.ForeColor = Color.White;
                dgv.Rows[0].DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font, FontStyle.Bold);

                // 取消选中任何单元格
                dgv.ClearSelection();

                // 显示到最后一行
                // dgv.FirstDisplayedScrollingRowIndex = dgv.Rows.Count - 1;
            }

            dgv.Visible = true;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "uiPagination_BetOrderReport_PageChangedError", ex.ToString());
            // this.ShowErrorTip("查询答题记录失败，请检查数据库连接或联系管理员");
        }
    }

    #endregion

    #region 飞单汇总

    /// <summary>
    /// 查询飞单汇总
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void comboBox_SelectIssueForHuiZong_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            DataGridView? dgv = dataGridView_HuiZongReport;
            dgv.Visible = false;
            dgv.DataSource = null;

            List<HuiZong> list;
            int selectedIndex = comboBox_SelectIssueForHuiZong.SelectedIndex;
            if (selectedIndex.Equals(0))
            {
                list = await DbHelper.FSql.Select<HuiZong>()
                    .ToListAsync();

                // var totalBalance = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.TotalBalance);
                //
                // var eatBalance = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.EatBalance);
                //
                // var toBetBalance = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.ToBetBalance);
                //
                // var toBetWinLoseMoney = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.结算);
                //
                // list.Insert(0, new HuiZong { Issue = "查询", Content = "合计", TotalBalance = totalBalance, EatBalance = eatBalance, ToBetBalance = toBetBalance, 结算 = toBetWinLoseMoney });
                // dgv.DataSource = list;
            }
            else if (selectedIndex.Equals(1))
            {
                list = await DbHelper.FSql.Select<HuiZong>()
                    .Where(a => a.BetResult.Equals(EnumBetResult.成功))
                    .ToListAsync();

                // var totalBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.成功))
                //     .ToList()
                //     .Sum(t => t.TotalBalance);
                //
                // var eatBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.成功))
                //     .ToList()
                //     .Sum(t => t.EatBalance);
                //
                // var toBetBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.成功))
                //     .ToList()
                //     .Sum(t => t.ToBetBalance);
                //
                // var toBetWinLoseMoney = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.结算);
                //
                // list.Insert(0, new HuiZong { Issue = "查询", Content = "合计", TotalBalance = totalBalance, EatBalance = eatBalance, ToBetBalance = toBetBalance, 结算 = toBetWinLoseMoney });
                // dgv.DataSource = list;
            }
            else if (selectedIndex.Equals(2))
            {
                list = await DbHelper.FSql.Select<HuiZong>()
                    .Where(a => a.BetResult.Equals(EnumBetResult.失败))
                    .ToListAsync();

                // var totalBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.失败))
                //     .ToList()
                //     .Sum(t => t.TotalBalance);
                // var eatBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.失败))
                //     .ToList()
                //     .Sum(t => t.EatBalance);
                // var toBetBalance = DbHelper.FSql.Select<HuiZong>()
                //     .Where(a => a.BetResult.Equals(EnumBetResult.失败))
                //     .ToList()
                //     .Sum(t => t.ToBetBalance);
                //
                // var toBetWinLoseMoney = DbHelper.FSql.Select<HuiZong>()
                //     .ToList()
                //     .Sum(t => t.结算);
                //
                // list.Insert(0, new HuiZong { Issue = "查询", Content = "合计", TotalBalance = totalBalance, EatBalance = eatBalance, ToBetBalance = toBetBalance, 结算 = toBetWinLoseMoney });
                // dgv.DataSource = list;
            }
            else
            {
                string issue = comboBox_SelectIssueForHuiZong.Text.Trim();
                list = await DbHelper.FSql.Select<HuiZong>()
                    .Where(t => t.Issue.Equals(issue))
                    .ToListAsync();
            }

            // 计算汇总数据
            decimal totalBalance = list.Sum(t => t.TotalBalance);
            decimal eatBalance = list.Sum(t => t.EatBalance);
            decimal toBetBalance = list.Sum(t => t.ToBetBalance);
            decimal toEatWinLoseMoney = list.Where(a => a.吃单结算 > 0).Sum(t => t.吃单结算) - list.Sum(t => t.EatBalance);
            decimal toBetWinLoseMoney = list.Where(a => a.飞单结算 > 0).Sum(t => t.飞单结算) - list.Sum(t => t.ToBetBalance);

            // 插入汇总数据
            list.Insert(0, new HuiZong
            {
                Issue = "汇总",
                Content = "合计",
                TotalBalance = totalBalance,
                EatBalance = eatBalance,
                ToBetBalance = toBetBalance,
                吃单结算 = Math.Round(toEatWinLoseMoney, 2),
                飞单结算 = Math.Round(toBetWinLoseMoney, 2)
            });

            // 绑定数据
            dgv.DataSource = list;

            // 设置列头标题
            dgv.Columns["Id"]!.HeaderText = @"Id";
            dgv.Columns["BetLottery"]!.HeaderText = @"游戏";
            dgv.Columns["Issue"]!.HeaderText = @"期号";
            dgv.Columns["Content"]!.HeaderText = @"项目";
            dgv.Columns["TotalBalance"]!.HeaderText = @"金额";
            dgv.Columns["EatBalance"]!.HeaderText = @"吃单";
            dgv.Columns["ToBetBalance"]!.HeaderText = @"飞单";
            dgv.Columns["BetResult"]!.HeaderText = @"飞单结果";

            // 设置列宽
            if (dgv.Rows.Count > 0)
            {
                // 设置列宽
                dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                // 设置列对齐方式
                dgv.Columns["飞单结算"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["吃单结算"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["赔率"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["开奖结果"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["BetResult"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["ToBetBalance"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["EatBalance"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["TotalBalance"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns["Content"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns["Issue"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                // 设置第1行的背景颜色为绿色，字体为白色
                dgv.Rows[0].DefaultCellStyle.BackColor = Color.Green;
                dgv.Rows[0].DefaultCellStyle.ForeColor = Color.White;
                dgv.Rows[0].DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font, FontStyle.Bold);

                // 设置失败的行的背景颜色
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    string val = row.Cells[^1].Value.ToString()!;
                    if (val.Equals("Fail"))
                    {
                        // 设置第1列的背景颜色为深红色，字体为白色
                        row.Cells[^1].Style.ForeColor = Color.Red;
                        row.Cells[^1].Style.Font = new Font(dgv.DefaultCellStyle.Font, FontStyle.Bold);
                    }
                }

                // 取消选中任何单元格
                dgv.ClearSelection();

                // 显示到最后一行
                // dgv.FirstDisplayedScrollingRowIndex = dgv.Rows.Count - 1;
            }

            dgv.Visible = true;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectIssueForHuiZong_SelectedIndexChangedError", ex.ToString());
            // this.ShowErrorTip("查询答题记录失败，请检查数据库连接或联系管理员");
        }
    }

    #endregion

    #region 上分报表

    private async void comboBox_SelectObjForAddMoneyReport_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            DataGridView? dgv = dataGridView_AddMoneyReport;
            dgv.Visible = false;
            dgv.DataSource = null;

            if (comboBox_SelectObjForAddMoneyReport.SelectedIndex.Equals(0))
            {
                var list = DbHelper.FSql.Select<AddMoney, Member>()
                    .LeftJoin(t => t.t1.Account == t.t2.Account)
                    .ToList(t => new
                    {
                        申请时间 = t.t1.Time,
                        账号 = t.t1.Account,
                        t.t2.备注名,
                        积分 = t.t1.Money,
                        状态 = t.t1.Status,
                        对应消息Id = t.t1.FromMsgId,
                        审核时间 = t.t1.PreTime,
                        // 时效 = (t.t1.PreTime - t.t1.Time).TotalSeconds
                    });

                dgv.DataSource = list;
            }
            else
            {
                string account = Ai.GetTextMiddle(comboBox_SelectObjForAddMoneyReport.Text.Trim(), Ai.中括号左, Ai.中括号右);
                var list = DbHelper.FSql.Select<AddMoney, Member>()
                    .LeftJoin(t => t.t1.Account == t.t2.Account)
                    .Where(t => t.t1.Account.Equals(account))
                    .ToList(t => new
                    {
                        申请时间 = t.t1.Time,
                        账号 = t.t1.Account,
                        t.t2.备注名,
                        积分 = t.t1.Money,
                        状态 = t.t1.Status,
                        对应消息Id = t.t1.FromMsgId,
                        审核时间 = t.t1.PreTime,
                        // 时效 = (t.t1.PreTime - t.t1.Time).TotalSeconds
                    });

                dgv.DataSource = list;
            }

            if (dgv.Rows.Count > 0)
            {
                // 设置列宽
                dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                // 设置列对齐方式
                dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgv.Columns[^6].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.Columns[^7].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                // 显示到最后一行
                dgv.FirstDisplayedScrollingRowIndex = dgv.Rows.Count - 1;
            }

            dgv.Visible = true;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "uiPagination_BetOrderReport_PageChangedError", ex.ToString());
            // this.ShowErrorTip("查询答题记录失败，请检查数据库连接或联系管理员");
        }
    }

    #endregion

    #region 下分报表

    private async void comboBox_SelectObjForSubMoneyReport_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            await Task.Run(SelectSubMoneyReport);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectObjForSubMoneyReport_SelectedIndexChangedError", ex.ToString());
        }
    }

    private void SelectSubMoneyReport()
    {
        Invoke(async () =>
        {
            try
            {
                DataGridView? dgv = dataGridView_SubMoneyReport;
                dgv.Visible = false;

                if (comboBox_SelectObjForSubMoneyReport.SelectedIndex.Equals(0))
                {
                    var list = DbHelper.FSql.Select<SubMoney, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .ToList(t => new
                        {
                            申请时间 = t.t1.Time,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            积分 = t.t1.Money,
                            状态 = t.t1.Status,
                            对应消息Id = t.t1.FromMsgId,
                            处理时间 = t.t1.PreTime,
                            // 时效 = (t.t1.PreTime - t.t1.Time).TotalSeconds
                        });

                    dgv.DataSource = list;
                }
                else
                {
                    string account = Ai.GetTextMiddle(comboBox_SelectObjForSubMoneyReport.Text.Trim(), Ai.中括号左, Ai.中括号右);
                    var list = DbHelper.FSql.Select<SubMoney, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .Where(t => t.t1.Account.Equals(account))
                        .ToList(t => new
                        {
                            申请时间 = t.t1.Time,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            积分 = t.t1.Money,
                            状态 = t.t1.Status,
                            对应消息Id = t.t1.FromMsgId,
                            处理时间 = t.t1.PreTime,
                            // 时效 = (t.t1.PreTime - t.t1.Time).TotalSeconds
                        });

                    dgv.DataSource = list;
                }

                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                    // 设置列对齐方式
                    dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^6].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^7].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                    // 显示到最后一行
                    dgv.FirstDisplayedScrollingRowIndex = dgv.Rows.Count - 1;
                }

                dgv.Visible = true;
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "SelectSubMoneyReportError", ex.ToString());
                // this.ShowErrorTip("查询下分记录失败，请检查数据库连接或联系管理员");
            }
        });
    }

    #endregion

    #region 财务报表

    private async void comboBox_SelectObjForFinanceReport_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            await Task.Run(SelectFinanceReport);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectObjForFinanceReport_SelectedIndexChangedError", ex.ToString());
        }
    }

    private void SelectFinanceReport()
    {
        Invoke(async () =>
        {
            try
            {
                DataGridView? dgv = dataGridView_FinanceReport;
                dgv.Visible = false;
                dgv.DataSource = null;

                if (comboBox_SelectObjForFinanceReport.SelectedIndex.Equals(0))
                {
                    var list = DbHelper.FSql.Select<Finance, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .ToList(t => new
                        {
                            t.t1.时间,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            变动前 = Math.Round(t.t1.变动前, 2),
                            变动值 = Math.Round(t.t1.变动值, 2),
                            变动后 = Math.Round(t.t1.变动后, 2),
                            t.t1.凭据,
                            对应消息Id = t.t1.对应信息
                        });

                    dgv.DataSource = list;
                }
                else
                {
                    string account = Ai.GetTextMiddle(comboBox_SelectObjForFinanceReport.Text.Trim(), Ai.中括号左, Ai.中括号右);
                    var list = DbHelper.FSql.Select<Finance, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .Where(t => t.t1.Account.Equals(account))
                        .ToList(t => new
                        {
                            t.t1.时间,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            变动前 = Math.Round(t.t1.变动前, 2),
                            变动值 = Math.Round(t.t1.变动值, 2),
                            变动后 = Math.Round(t.t1.变动后, 2),
                            t.t1.凭据,
                            对应消息Id = t.t1.对应信息
                        });

                    dgv.DataSource = list;
                }

                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                    // 设置列对齐方式
                    dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
                    dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

                    // 取消选中任何单元格
                    dgv.ClearSelection();
                }

                dgv.Visible = true;
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "SelectFinanceReportError", ex.ToString());
                // this.ShowErrorTip("查询财务记录失败，请检查数据库连接或联系管理员");
            }
        });
    }

    #endregion

    #region 消息报表

    private async void comboBox_SelectMemberForShowRecMsg_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            await Task.Run(ShowRecMsgReport);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_SelectMemberForShowRecMsg_SelectedIndexChangedError", ex.ToString());
        }
    }

    private void ShowRecMsgReport()
    {
        Invoke(async () =>
        {
            try
            {
                DataGridView? dgv = dataGridView_RecMsg;
                dgv.Visible = false;
                dgv.DataSource = null;

                if (comboBox_SelectMemberForShowRecMsg.SelectedIndex.Equals(0))
                {
                    var list = DbHelper.FSql.Select<ReceiveMessage, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .ToList(t => new
                        {
                            t.t1.Id,
                            时间 = t.t1.ReceiveTime.ToString("MM-dd HH:mm:ss"),
                            群ID = t.t1.GroupId,
                            群名 = t.t1.GroupName,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            消息内容 = t.t1.Content,
                            处理时间 = t.t1.PreTime.ToString("MM-dd HH:mm:ss"),
                            // 时效 = (t.t1.PreTime - t.t1.ReceiveTime).TotalMilliseconds
                        });

                    dgv.DataSource = list;
                }
                else
                {
                    string account = Ai.GetTextMiddle(comboBox_SelectMemberForShowRecMsg.Text.Trim(), Ai.中括号左, Ai.中括号右);
                    var list = DbHelper.FSql.Select<ReceiveMessage, Member>()
                        .LeftJoin(t => t.t1.Account == t.t2.Account)
                        .Where(t => t.t1.Account.Equals(account))
                        .ToList(t => new
                        {
                            t.t1.Id,
                            时间 = t.t1.ReceiveTime.ToString("MM-dd HH:mm:ss"),
                            群ID = t.t1.GroupId,
                            群名 = t.t1.GroupName,
                            账号 = t.t1.Account,
                            t.t2.备注名,
                            消息内容 = t.t1.Content,
                            处理时间 = t.t1.PreTime.ToString("MM-dd HH:mm:ss"),
                            // 时效 = (t.t1.PreTime - t.t1.ReceiveTime).TotalMilliseconds
                        });

                    dgv.DataSource = list;
                }


                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                    // 设置列对齐方式
                    dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
                    dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns[^6].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }

                dgv.Visible = true;
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "SelectRecMsgError", ex.ToString());
                // this.ShowErrorTip("查询消息记录失败，请检查数据库连接或联系管理员");
            }
        });
    }

    #endregion

    #region 显示日志

    private List<Log> LogList { get; set; } = new();

    private async void ShowLog()
    {
        try
        {
            LogList = await DbHelper.FSql.Select<Log>().ToListAsync();
            Invoke(() => { dataGridView_Log.RowCount = LogList.Count; });
            Invoke(() =>
            {
                // dataGridView_Log滚动到最后一行
                dataGridView_Log.FirstDisplayedScrollingRowIndex = LogList.Count - 1;
            });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "LogReport_Error", ex.ToString());
        }
    }

    private void dataGridView_Log_CellValueNeeded(object? sender, DataGridViewCellValueEventArgs e)
    {
        // 根据行索引和列索引提供单元格的值
        e.Value = GetCellValue(e.RowIndex, e.ColumnIndex);
    }

    private object? GetCellValue(int rowIndex, int columnIndex)
    {
        try
        {
            // 这里根据行列索引返回对应的值，示例返回行号
            if (rowIndex < 0 || rowIndex >= LogList.Count)
            {
                return null; // 或者处理异常
            }

            Log log = LogList[rowIndex];
            switch (columnIndex)
            {
                case 0:
                    return log.Id;
                case 1:
                    return log.Type;
                case 2:
                    return log.Time;
                case 3:
                    return log.Title;
                case 4:
                    return log.Content;
                default:
                    return null;
            }
        }
        catch
        {
            // ignored
        }

        return null;
    }

    #endregion

    #region 复制平台日志

    /// <summary>
    /// 复制平台日志
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void dataGridView_PlatformLog_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0)
        {
            string log = dataGridView_PlatformLog.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString()!;
            Clipboard.SetText(log);
            // this.ShowSuccessTip("记录已复制到剪贴板.");
        }
    }

    #endregion

    #region 复制记录日志

    /// <summary>
    /// 复制记录日志
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void dataGridView_Log_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0)
        {
            string log = dataGridView_Log.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString()!;
            Clipboard.SetText(log);
            // this.ShowSuccessTip("记录已复制到剪贴板.");
        }
    }

    #endregion
}