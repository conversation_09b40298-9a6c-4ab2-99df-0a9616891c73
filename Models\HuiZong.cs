﻿using FreeSql.DataAnnotations;
using Robot.Enum;

namespace Robot.Models;

[Table(Name = "HuiZong")]
public class HuiZong
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    public EnumBetLottery BetLottery { get; set; }
    public string Issue { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public decimal TotalBalance { get; set; }
    public decimal EatBalance { get; set; }
    public decimal ToBetBalance { get; set; }
    public EnumBetResult BetResult { get; set; } = EnumBetResult.未知;
    public int 开奖结果 { get; set; }
    public decimal 赔率 { get; set; }
    public decimal 吃单结算 { get; set; }
    public decimal 飞单结算 { get; set; }
}