using Robot.Domain.Common;
using Robot.Domain.ValueObjects;
using Robot.Shared.Enums;
using Robot.Shared.Constants;

namespace Robot.Domain.Entities;

/// <summary>
/// 会员实体
/// </summary>
public class Member : BaseEntity
{
    /// <summary>
    /// 私有构造函数，用于ORM
    /// </summary>
    private Member() { }
    
    /// <summary>
    /// 创建会员
    /// </summary>
    public Member(string account, string nickname, decimal rebateRate = GameConstants.DefaultRebateRate)
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException("账号不能为空", nameof(account));
            
        Account = account;
        Nickname = nickname ?? string.Empty;
        DisplayName = nickname ?? string.Empty;
        Balance = Money.Zero;
        RebateRate = rebateRate;
        IsBot = false;
        ParentAccount = string.Empty;
        CommissionRate = 0m;
    }
    
    /// <summary>
    /// 账号（主键）
    /// </summary>
    public string Account { get; private set; } = string.Empty;
    
    /// <summary>
    /// 昵称
    /// </summary>
    public string Nickname { get; private set; } = string.Empty;
    
    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; private set; } = string.Empty;
    
    /// <summary>
    /// 余额
    /// </summary>
    public Money Balance { get; private set; } = Money.Zero;
    
    /// <summary>
    /// 回水比例
    /// </summary>
    public decimal RebateRate { get; private set; }
    
    /// <summary>
    /// 是否为机器人
    /// </summary>
    public bool IsBot { get; private set; }
    
    /// <summary>
    /// 上级账号
    /// </summary>
    public string ParentAccount { get; private set; } = string.Empty;
    
    /// <summary>
    /// 返利比例
    /// </summary>
    public decimal CommissionRate { get; private set; }
    
    /// <summary>
    /// 更新昵称
    /// </summary>
    public void UpdateNickname(string nickname)
    {
        if (!string.IsNullOrWhiteSpace(nickname) && Nickname != nickname)
        {
            Nickname = nickname;
            if (string.IsNullOrWhiteSpace(DisplayName))
            {
                DisplayName = nickname;
            }
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 更新显示名称
    /// </summary>
    public void UpdateDisplayName(string displayName)
    {
        if (!string.IsNullOrWhiteSpace(displayName) && DisplayName != displayName)
        {
            DisplayName = displayName;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 充值
    /// </summary>
    public void Deposit(Money amount)
    {
        if (amount.Value <= 0)
            throw new ArgumentException("充值金额必须大于0", nameof(amount));
            
        Balance = Balance.Add(amount);
        MarkAsUpdated();
        
        // 添加充值领域事件
        AddDomainEvent(new MemberDepositedEvent(Account, amount, DateTime.Now));
    }
    
    /// <summary>
    /// 扣款
    /// </summary>
    public void Withdraw(Money amount)
    {
        if (amount.Value <= 0)
            throw new ArgumentException("扣款金额必须大于0", nameof(amount));
            
        if (Balance.Value < amount.Value)
            throw new InvalidOperationException("余额不足");
            
        Balance = Balance.Subtract(amount);
        MarkAsUpdated();
        
        // 添加扣款领域事件
        AddDomainEvent(new MemberWithdrewEvent(Account, amount, DateTime.Now));
    }
    
    /// <summary>
    /// 设置为机器人
    /// </summary>
    public void SetAsBot()
    {
        if (!IsBot)
        {
            IsBot = true;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 设置上级账号
    /// </summary>
    public void SetParentAccount(string parentAccount)
    {
        if (ParentAccount != parentAccount)
        {
            ParentAccount = parentAccount ?? string.Empty;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 更新回水比例
    /// </summary>
    public void UpdateRebateRate(decimal rebateRate)
    {
        if (rebateRate < 0)
            throw new ArgumentException("回水比例不能为负数", nameof(rebateRate));
            
        if (RebateRate != rebateRate)
        {
            RebateRate = rebateRate;
            MarkAsUpdated();
        }
    }
    
    /// <summary>
    /// 更新返利比例
    /// </summary>
    public void UpdateCommissionRate(decimal commissionRate)
    {
        if (commissionRate < 0)
            throw new ArgumentException("返利比例不能为负数", nameof(commissionRate));
            
        if (CommissionRate != commissionRate)
        {
            CommissionRate = commissionRate;
            MarkAsUpdated();
        }
    }
}

/// <summary>
/// 会员充值事件
/// </summary>
public record MemberDepositedEvent(string Account, Money Amount, DateTime OccurredOn) : IDomainEvent;

/// <summary>
/// 会员扣款事件
/// </summary>
public record MemberWithdrewEvent(string Account, Money Amount, DateTime OccurredOn) : IDomainEvent;
