using Robot.Shared.Enums;

namespace Robot.Domain.Interfaces;

/// <summary>
/// 聊天平台适配器接口
/// </summary>
public interface IChatPlatformAdapter
{
    /// <summary>
    /// 平台类型
    /// </summary>
    ChatPlatformType PlatformType { get; }
    
    /// <summary>
    /// 获取机器人信息
    /// </summary>
    Task<RobotInfo> GetRobotInfoAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取群组列表
    /// </summary>
    Task<Dictionary<string, string>> GetGroupListAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户昵称
    /// </summary>
    Task<string> GetUserNicknameAsync(string userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送群消息
    /// </summary>
    Task<bool> SendGroupMessageAsync(string message, string? targetUserId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送图片
    /// </summary>
    Task<bool> SendImageAsync(string imagePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送私聊消息
    /// </summary>
    Task<bool> SendPrivateMessageAsync(string userId, string message, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查平台连接状态
    /// </summary>
    Task<bool> CheckConnectionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 机器人信息
/// </summary>
public record RobotInfo(string Account, string NickName);

/// <summary>
/// 聊天平台工厂接口
/// </summary>
public interface IChatPlatformFactory
{
    /// <summary>
    /// 创建聊天平台适配器
    /// </summary>
    IChatPlatformAdapter CreateAdapter(ChatPlatformType platformType);
    
    /// <summary>
    /// 获取支持的平台类型
    /// </summary>
    IEnumerable<ChatPlatformType> GetSupportedPlatforms();
}
