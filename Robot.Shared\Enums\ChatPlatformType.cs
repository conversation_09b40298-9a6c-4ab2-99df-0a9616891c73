namespace Robot.Shared.Enums;

/// <summary>
/// 聊天平台类型枚举
/// </summary>
public enum ChatPlatformType
{
    /// <summary>
    /// 微信360018版本
    /// </summary>
    WeChat360018 = 1,
    
    /// <summary>
    /// 微信391016版本
    /// </summary>
    WeChat391016 = 2,
    
    /// <summary>
    /// 微信391125版本
    /// </summary>
    WeChat391125 = 3,
    
    /// <summary>
    /// 微信391216版本
    /// </summary>
    WeChat391216 = 4,
    
    /// <summary>
    /// QQ平台
    /// </summary>
    QQ = 5,
    
    /// <summary>
    /// MyQQ平台
    /// </summary>
    MyQQ = 6,
    
    /// <summary>
    /// GoQQ平台
    /// </summary>
    GoQQ = 7,
    
    /// <summary>
    /// LaQQ平台
    /// </summary>
    LaQQ = 8,
    
    /// <summary>
    /// 一起聊吧平台
    /// </summary>
    OneChat = 9,
    
    /// <summary>
    /// VoceChat平台
    /// </summary>
    VoceChat = 10
}
