﻿using AiHelper;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

/// <summary>
/// 设置上级账户窗体类
///
/// 功能概述：
/// 1. 为用户设置拉手上级账户，建立上下级关系
/// 2. 配置返利比例，用于计算上级的返利收益
/// 3. 提供用户友好的下拉选择界面，避免输入错误
/// 4. 进行数据验证，确保返利比例在合理范围内
/// 5. 支持模态对话框操作，确保数据设置的完整性
///
/// 业务背景：
/// 在彩票代理系统中，存在多级代理关系：
/// - 拉手上级：指推荐当前用户的上级代理
/// - 返利比例：上级代理从下级用户投注中获得的返利百分比
/// - 代理层级：形成树状的代理关系网络
///
/// 设计特点：
/// - 模态对话框设计，确保操作完整性
/// - 异步数据加载，提升用户体验
/// - 完善的异常处理和日志记录
/// - 数据验证机制，防止无效输入
/// - 用户友好的错误提示
///
/// 使用场景：
/// - 新用户注册时设置上级关系
/// - 修改现有用户的上级代理
/// - 调整返利比例配置
/// - 代理关系管理和维护
///
/// 使用示例：
/// ```csharp
/// var setParentForm = new FormSetParentAccount();
/// if (setParentForm.ShowDialog() == DialogResult.OK)
/// {
///     string parentAccount = setParentForm.拉手上级;
///     decimal rebateRatio = setParentForm.返利比例;
///     // 处理设置结果
/// }
/// ```
///
/// 数据流程：
/// 1. 窗体加载 → 从数据库获取所有用户列表
/// 2. 用户选择 → 选择上级账户和输入返利比例
/// 3. 数据验证 → 检查返利比例是否在有效范围内
/// 4. 结果返回 → 通过属性返回设置的值给调用方
/// </summary>
public partial class FormSetParentAccount : Form
{
    #region 私有字段和公共属性

    /// <summary>
    /// 窗体标题属性
    ///
    /// 功能说明：
    /// 用于存储窗体的标题信息，虽然当前版本未使用，
    /// 但保留此属性以便未来扩展窗体标题的动态设置功能
    ///
    /// 设计考虑：
    /// - 为未来的功能扩展预留接口
    /// - 保持与其他窗体类的一致性
    /// - 支持多语言或动态标题的可能性
    /// </summary>
    private string Title { get; set; } = string.Empty;

    /// <summary>
    /// 拉手上级账户属性
    ///
    /// 功能说明：
    /// 存储用户选择的上级代理账户名称，这是建立代理关系的核心数据
    ///
    /// 业务含义：
    /// - 拉手：在代理系统中指推荐新用户的代理人
    /// - 上级：在代理层级中的上一级代理
    /// - 账户：系统中唯一标识用户的账户名
    ///
    /// 数据来源：
    /// 从下拉框选择的文本中提取，格式为"[账户名]昵称"，
    /// 通过字符串解析获取中括号内的账户名
    ///
    /// 访问控制：
    /// - public get：允许外部读取设置结果
    /// - private set：防止外部直接修改，确保数据完整性
    /// </summary>
    public string 拉手上级 { get; private set; } = string.Empty;

    /// <summary>
    /// 返利比例属性
    ///
    /// 功能说明：
    /// 设置上级代理从下级用户投注中获得的返利百分比
    ///
    /// 业务规则：
    /// - 取值范围：0.0 - 2.5（对应0% - 2.5%）
    /// - 精度：支持小数点后多位精度
    /// - 计算方式：下级用户投注金额 × 返利比例 = 上级返利金额
    ///
    /// 使用示例：
    /// - 返利比例 = 1.5，表示上级获得下级投注金额的1.5%作为返利
    /// - 下级投注1000元，上级获得15元返利
    ///
    /// 数据验证：
    /// 在确定按钮点击时会验证此值是否在有效范围内
    /// </summary>
    public decimal 返利比例 { get; set; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    ///
    /// 功能说明：
    /// 初始化窗体组件，准备用户界面元素
    ///
    /// 初始化内容：
    /// - 调用设计器生成的组件初始化方法
    /// - 设置窗体的基本属性和控件布局
    /// - 准备数据绑定和事件处理
    ///
    /// 注意事项：
    /// 实际的数据加载在Load事件中进行，避免构造函数中的耗时操作
    /// </summary>
    public FormSetParentAccount()
    {
        // 初始化窗体设计器生成的组件
        // 包括控件创建、布局设置、事件绑定等
        InitializeComponent();
    }

    #endregion

    #region 窗体事件处理

    /// <summary>
    /// 窗体加载事件处理方法
    ///
    /// 功能说明：
    /// 在窗体显示时异步加载用户数据到下拉框中
    ///
    /// 执行流程：
    /// 1. 异步调用数据加载方法
    /// 2. 填充下拉框选项
    /// 3. 设置默认选择项
    /// 4. 处理可能的异常情况
    ///
    /// 异步设计：
    /// 使用async/await模式，避免UI线程阻塞，提升用户体验
    ///
    /// 异常处理：
    /// 捕获所有异常并记录到系统日志，确保窗体能正常显示
    /// 即使数据加载失败，用户仍可以看到窗体界面
    ///
    /// 用户体验：
    /// - 窗体立即显示，数据在后台加载
    /// - 加载失败时不会导致程序崩溃
    /// - 错误信息记录到日志便于问题排查
    /// </summary>
    /// <param name="sender">事件发送者（当前窗体）</param>
    /// <param name="e">事件参数</param>
    private async void FormSetParentAccount_Load(object sender, EventArgs e)
    {
        try
        {
            // 异步加载用户数据到下拉框
            // 这个操作可能涉及数据库查询，使用异步避免UI冻结
            await AddMemberToComboBox(comboBox_Member);
        }
        catch (Exception ex)
        {
            // 记录异常到系统日志，便于问题排查和系统监控
            // 使用机器人日志类型，表示这是系统内部操作产生的日志
            await DbHelper.AddLog(EnumLogType.机器人, @"AddMemberToComboBoxError", ex.ToString());
        }
    }

    #endregion

    #region 私有方法 - 数据操作

    /// <summary>
    /// 将用户数据添加到下拉框的异步方法
    ///
    /// 功能说明：
    /// 1. 从数据库查询所有用户信息
    /// 2. 清空下拉框现有选项
    /// 3. 添加默认提示选项
    /// 4. 遍历用户列表并格式化显示
    /// 5. 设置默认选择项
    ///
    /// 数据格式：
    /// 下拉框显示格式："[账户名]昵称"
    /// - 中括号内是账户的唯一标识
    /// - 中括号外是用户的显示昵称
    /// - 这种格式便于用户识别，也便于程序解析
    ///
    /// 异步设计原因：
    /// - 数据库查询可能耗时，避免阻塞UI线程
    /// - 提升用户体验，窗体响应更流畅
    /// - 支持大量用户数据的加载
    ///
    /// 异常处理策略：
    /// - 捕获数据库操作异常
    /// - 记录详细错误信息到日志
    /// - 不向用户显示技术性错误信息
    /// - 确保程序继续运行
    ///
    /// 性能考虑：
    /// - 一次性加载所有用户，避免频繁查询
    /// - 使用异步操作，不阻塞用户界面
    /// - 异常情况下优雅降级
    /// </summary>
    /// <param name="comboBox">要填充数据的下拉框控件</param>
    /// <returns>异步任务，表示操作的完成状态</returns>
    private async Task AddMemberToComboBox(ComboBox comboBox)
    {
        try
        {
            // 从数据库异步查询所有用户信息
            // 使用FreeSql ORM进行数据访问，支持异步操作
            List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync();

            // 清空下拉框现有的所有选项
            // 确保每次加载都是最新的完整数据
            comboBox.Items.Clear();

            // 添加默认的提示选项
            // 提醒用户需要进行选择，同时作为验证的依据
            comboBox.Items.Add("请选择拉手上级");

            // 遍历所有用户，添加到下拉框选项中
            foreach (Member member in memberList)
            {
                // 格式化用户信息显示
                // 格式：[账户名]昵称
                // 使用Ai.中括号左和Ai.中括号右确保格式统一
                // 这样便于后续解析账户名
                comboBox.Items.Add($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
            }

            // 设置默认选择第一项（提示选项）
            // 确保用户看到提示信息，避免意外选择
            comboBox.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            // 记录数据加载异常到系统日志
            // 包含完整的异常信息，便于问题诊断
            await DbHelper.AddLog(EnumLogType.机器人, "AddMemberToComboBoxError", ex.ToString());

            // 注意：这里不向用户显示错误信息
            // 因为数据加载失败时，用户仍可以看到空的下拉框
            // 避免技术性错误信息影响用户体验
        }
    }

    #endregion

    #region 按钮事件处理

    /// <summary>
    /// 确定按钮点击事件处理方法
    ///
    /// 功能说明：
    /// 处理用户点击确定按钮的操作，验证输入数据并保存设置结果
    ///
    /// 执行流程：
    /// 1. 验证用户是否选择了有效的上级账户
    /// 2. 从下拉框文本中解析出账户名
    /// 3. 验证返利比例输入的有效性
    /// 4. 检查返利比例是否在业务规则范围内
    /// 5. 设置对话框结果并关闭窗体
    ///
    /// 数据验证规则：
    /// - 必须选择有效的上级账户（不能是提示选项）
    /// - 返利比例必须是有效的数字格式
    /// - 返利比例必须在0-2.5之间
    ///
    /// 错误处理策略：
    /// - 输入格式错误：显示用户友好的错误提示
    /// - 数值范围错误：显示具体的范围要求
    /// - 系统异常：记录日志但不影响用户操作
    ///
    /// 用户体验设计：
    /// - 清晰的错误提示信息
    /// - 错误时不关闭窗体，允许用户重新输入
    /// - 成功时自动关闭窗体并返回结果
    ///
    /// 业务规则：
    /// - 返利比例0-2.5对应实际的0%-2.5%
    /// - 使用现代C#语法进行范围检查
    /// - 支持小数精度的返利比例设置
    /// </summary>
    /// <param name="sender">事件发送者（确定按钮）</param>
    /// <param name="e">事件参数</param>
    private async void button_Sure_Click(object sender, EventArgs e)
    {
        try
        {
            // 验证用户是否选择了有效的上级账户
            // SelectedIndex > 0 表示不是默认的提示选项
            if (comboBox_Member.SelectedIndex > 0)
            {
                // 从下拉框选择的文本中解析账户名
                // 使用AiHelper工具类提取中括号内的内容
                // 格式："[账户名]昵称" → 提取出"账户名"
                拉手上级 = Ai.GetTextMiddle(comboBox_Member.Text, Ai.中括号左, Ai.中括号右);

                try
                {
                    // 尝试将用户输入的返利比例转换为decimal类型
                    // Trim()去除前后空格，避免格式问题
                    返利比例 = Convert.ToDecimal(textBox_返利比例.Text.Trim());

                    // 验证返利比例是否在业务规则范围内
                    // 使用现代C# pattern matching语法进行范围检查
                    // 范围：0 ≤ 返利比例 ≤ 2.5
                    if (返利比例 is < 0 or > (decimal)2.5)
                    {
                        // 显示用户友好的错误提示
                        // 明确说明有效的输入范围
                        MessageBox.Show(@"返利比例输入错误，请重新输入！参考范围为0-2.5之间");
                        return; // 不关闭窗体，允许用户重新输入
                    }

                    // 所有验证通过，设置对话框结果为成功
                    DialogResult = DialogResult.OK;

                    // 关闭窗体，返回到调用方
                    // 调用方可以通过DialogResult判断操作是否成功
                    // 并通过属性获取设置的值
                    Close();
                }
                catch (Exception ex)
                {
                    // 记录数据转换异常到系统日志
                    // 这通常是用户输入了非数字格式的内容
                    await DbHelper.AddLog(EnumLogType.机器人, "Convert.ToDecimalError", ex.ToString());

                    // 向用户显示友好的错误提示
                    // 不显示技术性的异常信息，避免用户困惑
                    MessageBox.Show(@"设置返利比例输入错误，请重新输入！");

                    // 不关闭窗体，允许用户重新输入正确的格式
                }
            }
            // 如果用户没有选择有效的上级账户，不执行任何操作
            // 窗体保持打开状态，等待用户进行正确的选择
        }
        catch (Exception ex)
        {
            // 记录按钮点击处理过程中的意外异常
            // 这种异常通常是系统级别的问题
            await DbHelper.AddLog(EnumLogType.机器人, "button_Sure_ClickError", ex.ToString());

            // 注意：这里不向用户显示错误信息
            // 因为这种异常通常是系统问题，用户无法解决
            // 记录日志便于开发人员排查问题
        }
    }

    #endregion
}