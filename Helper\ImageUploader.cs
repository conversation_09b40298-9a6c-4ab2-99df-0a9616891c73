using Flurl.Http;

namespace Robot.Helper
{
    public class ImageUploader
    {
        private readonly string _baseUrl;
        private readonly string _uid;

        public ImageUploader(string uid, string baseUrl = "http://127.0.0.1:3001")
        {
            _uid = uid;
            _baseUrl = baseUrl;
        }

        /// <summary>
        /// 上传图片文件
        /// </summary>
        /// <param name="imagePath">图片文件的完整路径</param>
        /// <returns>上传后的图片URL</returns>
        public async Task<string> UploadImageAsync(string imagePath)
        {
            try
            {
                var response = await $"{_baseUrl}/api/upload"
                    .PostMultipartAsync(mp => mp
                        .AddFile("image", imagePath)
                        .AddString("uid", _uid));

                var result = await response.GetJsonAsync<ImageResponse>();
                return result.ImageUrl;
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseJsonAsync<ErrorResponse>();
                throw new Exception(error?.Error ?? ex.Message);
            }
        }
    }

    public class ImageResponse
    {
        public string ImageUrl { get; set; }
    }

    public class ErrorResponse
    {
        public string Error { get; set; }
    }

    // 使用示例
    public class Program
    {
        public static async Task Main()
        {
            try
            {
                // 创建上传器实例（使用已存在的用户ID）
                var uploader = new ImageUploader("user123");

                // 上传图片
                string imageUrl = await uploader.UploadImageAsync(@"C:\test.jpg");
                Console.WriteLine($"图片上传成功: {imageUrl}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"上传失败: {ex.Message}");
            }
        }
    }
}