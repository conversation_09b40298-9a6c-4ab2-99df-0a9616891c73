{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.93\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.93\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "43E661A39CD708B70308EABFA344471265B8D615094A9B0AD2DFD1C3D75CEB55"}, "default_search_provider_data": {"template_url_data": "8DD350A6A6ED89574AE67B7C2D46E10CEB262BB94728015964E55F3D0DFFAD41"}, "edge": {"services": {"account_id": "C7D7487C521AB590820D86529E54F2B3EF84A9113836921698345855F869F28E", "last_username": "FCDC3159339E76C84BC0CFDFBC671752A8F6875A75299020450C0C529EDE85E5"}}, "enterprise_signin": {"policy_recovery_token": "5B3B8C93F43CAF6C1985D9A0222E80C212310151E772514F8EFC31125A6FF099"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "FF9C6BC88086249BEF2C56C58BBBE69441450A7C865345FF154466568089CA54", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "13342BC2E5A896121D5D040F09B0AADDF6FD216D06E9DAB28FE66F641F96625D"}, "ui": {"developer_mode": "17CD76A27A9A200A9C5DEA7BE0AE53512EF63815494D960AFD3420590F1FEF4D"}}, "google": {"services": {"last_signed_in_username": "60170BB09752FD69F2DC698B0D7B3A18E87A93AC81030A9AA33CEDB7BCE969A4"}}, "homepage": "E5E1DF37AB3578ABF02E41E5F0E66BF2F801835A4459D1A7943FF3F7A3C353E1", "homepage_is_newtabpage": "270D7CD8F8166F95F3C045D670F1DD2D5CE8CF59868F5EF2DF5BC5C38C0AFE4C", "media": {"cdm": {"origin_data": "0A8415F301E4D7DF4044E5C30F7AE255629ECC2DA01E98E9F30828787B2205ED"}, "storage_id_salt": "DBDFACBB578402AADBF9C5828DEA2252BAA78503135447A04EA927A378597EFB"}, "pinned_tabs": "C9F0A8690672E90533BE4BB46E352AB357ACCD4358E4AC90E2428EC3ABC24E56", "prefs": {"preference_reset_time": "26E10B578EFECB94920EFD7153012FA2CF083AD4639DDB1A86C45DE6ADE32E31"}, "safebrowsing": {"incidents_sent": "81DDE4D8927AA8189804973B115EB2752C1B94EFA49DA2456BB96DC3F50FD175"}, "search_provider_overrides": "14ACF03C220602560D9FFE3CADC10173AFA0D49A308329F6C991E0F51373854E", "session": {"restore_on_startup": "393A648FF8E9FE51081096EB73131DC222D82FD9C269074AF328C062F8C855D5", "startup_urls": "E0E9ADA300C1BE41E7D72AFEB22D360DB956D9D4C4698C9AD718E81ECFF95F22"}}, "super_mac": "27FE55643D242AB3E6CF8CC1863FDCD654F550C110B7E6AA130322EE0B7E6225"}}