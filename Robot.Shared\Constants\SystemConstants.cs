namespace Robot.Shared.Constants;

/// <summary>
/// 系统常量定义
/// </summary>
public static class SystemConstants
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public const string AppName = "KingRobot";
    
    /// <summary>
    /// 许可证服务器地址
    /// </summary>
    public const string LicenseServer = "http://**************";
    
    /// <summary>
    /// 默认平台地址
    /// </summary>
    public const string DefaultPlatformHost = "http://127.0.0.1:6000";
    
    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    public const string DefaultConnectionString = "Data Source=Robot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;";
    
    /// <summary>
    /// 服务启动标识键名
    /// </summary>
    public const string IsStartServiceKey = "IsStartService";
}

/// <summary>
/// 游戏相关常量
/// </summary>
public static class GameConstants
{
    /// <summary>
    /// 默认开盘时间（秒）
    /// </summary>
    public const int DefaultOpenTime = 260;
    
    /// <summary>
    /// 默认封盘时间（秒）
    /// </summary>
    public const int DefaultCloseTime = 30;
    
    /// <summary>
    /// 默认回水比例
    /// </summary>
    public const decimal DefaultRebateRate = 1.5m;
    
    /// <summary>
    /// 最低飞单金额
    /// </summary>
    public const int MinFlyBetAmount = 10;
    
    /// <summary>
    /// 默认最低限投
    /// </summary>
    public const decimal DefaultMinBet = 10m;
    
    /// <summary>
    /// 默认最高限投
    /// </summary>
    public const decimal DefaultMaxBet = 20000m;
}

/// <summary>
/// UI相关常量
/// </summary>
public static class UIConstants
{
    /// <summary>
    /// 表情符号映射
    /// </summary>
    public static class Emojis
    {
        public const string Star = "⭐";
        public const string CheckMark = "✅";
        public const string CrossMark = "❌";
        public const string Warning = "⚠️";
        public const string Money = "💰";
        public const string Trophy = "🏆";
    }
    
    /// <summary>
    /// 消息格式
    /// </summary>
    public static class MessageFormats
    {
        public const string LeftBracket = "[";
        public const string RightBracket = "]";
        public const string Separator = " | ";
    }
}
