﻿using AiHelper;
using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.ChatPlatform;

public static class VoceChatHelper
{
    private static string GroupId { get; set; } = "1";

    public static async Task GetRobotInfo()
    {
        RobotHelper.RobotInfo.Account = "admin";
        RobotHelper.RobotInfo.NickName = "系统管理员";
        await Task.Delay(0);
    }

    public static async Task GetGroup()
    {
        try
        {
            RobotHelper.GroupDic.TryAdd("VoceChat", "一起聊吧");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }
    }

    public static async Task<string> GetNick(string uid)
    {
        try
        {
            string url = $"{VoiceChat.Current.Host}/api/user/{uid}";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3))
                .GetStringAsync();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("email"))
            {
                JObject jObject = JObject.Parse(response);
                string nickName = jObject["name"]!.ToString();
                return nickName;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }

        return "";
    }

    public static async Task SendGroupMessage(string content)
    {
        try
        {
            content = content.Replace("\r", Environment.NewLine);
            string url = $"{VoiceChat.Current.Host}/api/bot/send_to_group/{GroupId}";

            await url
                .WithHeader("content-type", "text/plain")
                .WithHeader("x-api-key",  VoiceChat.Current.ApiKey)
                .PostStringAsync(content)
                .ReceiveString();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }
    }

    public static async Task SendGroupMessage(string content, string atAccount)
    {
        try
        {
            string nickName = (await DbHelper.FSql.Select<Member>().Where(a => a.Account.Equals(atAccount)).ToOneAsync()).昵称;
            content = $"@{nickName}{content}";
            content = content.Replace("\r", Environment.NewLine);
            string url = $"{VoiceChat.Current.Host}/api/bot/send_to_group/{GroupId}";
            await url
                .WithHeader("content-type", "text/plain")
                .WithHeader("x-api-key", VoiceChat.Current.ApiKey)
                .PostStringAsync(content)
                .ReceiveString();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
        }
    }

    public static async Task SendImage(string workGroupId, string imgPath)
    {
        // 准备文件
        string imgName = Ai.GetTextRight(imgPath, "images/");
        string? serverFilePath = string.Empty;
        string url = $"{VoiceChat.Current.Host}/api/bot/file/prepare";

        string fileId = await url
            .WithHeader("x-api-key", VoiceChat.Current.ApiKey)
            .PostJsonAsync(new
            {
                // content_type = "image/jpeg",
                content_type = "image/jpeg",
                filename = imgName //D:\SolutionRobot\Robot\bin\Debug\net8.0-windows\images\tan6.jpg
            })
            .ReceiveString();
        fileId = fileId.Trim('"');

        // 上传图片
        url = $"{VoiceChat.Current.Host}/api/bot/file/upload";
        try
        {
            string responseString = await url
                .WithHeader("x-api-key", VoiceChat.Current.ApiKey)
                .WithHeader("Accept", "application/json")
                .PostMultipartAsync(mp =>
                {
                    mp.AddString("file_id", fileId);
                    mp.AddFile("chunk_data", imgPath, "image/jpeg");
                    mp.AddString("chunk_is_last", "true");
                })
                .ReceiveString();

            if (responseString.Contains("image_properties"))
            {
                JObject jObject = JObject.Parse(responseString);
                serverFilePath = jObject["path"]?.ToString();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }

        // 发送图片
        if (!string.IsNullOrWhiteSpace(serverFilePath))
        {
            // 发送信息
            url = $"{VoiceChat.Current.Host}/api/bot/send_to_group/{GroupId}";
            string result = await url
                .WithHeader("content-type", "vocechat/file")
                .WithHeader("x-api-key", VoiceChat.Current.ApiKey)
                .PostJsonAsync(new
                {
                    path = serverFilePath
                })
                .ReceiveString();
            Console.WriteLine(result);
        }
    }

    public static async Task<string> AddUser(string content)
    {
        try
        {
            string randomId = Ai.GetRandomNum(6);
            string url = $"{VoiceChat.Current.Host}/api/admin/user";
            var response = await url
                .WithHeader("Accept", "application/json; charset=utf-8")
                .WithHeader("Content_Type", "application/json; charset=utf-8")
                .WithHeader("x-api-key", VoiceChat.Current.ApiKey)
                .PostJsonAsync(new
                {
                    email = $"{randomId}@qq.com",
                    password = "666888",
                    name = content,
                    gender = 0,
                    is_admin = true,
                    language = "ZH-CN",
                    birthday = 0,
                    webhook_url = "",
                    is_bot = false,
                    widget_id = ""
                })
                .ReceiveString();
            return response;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, ex.ToString());
            return ex.ToString();
        }
    }
}