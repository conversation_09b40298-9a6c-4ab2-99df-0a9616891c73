﻿using System.Collections.Concurrent;
using Robot.Enum;
using Robot.Models;

// ReSharper disable InvalidXmlDocComment

namespace Robot.Helper;

/// <summary>
/// 彩票期号时间管理器
///
/// 这是整个机器人系统的时间调度核心，负责管理彩票游戏的期号生成、时间控制和倒计时逻辑。
///
/// 核心职责：
/// 1. 期号时间生成 - 根据不同彩种规则生成期号和开奖时间，并存储到数据库
/// 2. 实时时间监控 - 循环监控当前期号状态和倒计时，每秒更新一次
/// 3. 开封盘控制 - 提供开盘和封盘的时间节点控制，支持主系统的业务逻辑
///
/// 支持的游戏类型：
/// - 台湾宾果：每天203期，从07:00开始，每5分钟一期，期号格式为{民国年份}{6位流水号}
/// - 168飞艇：每天180期，从13:05开始，每5分钟一期，期号格式为{yyyyMMdd}{3位当日期号}
///
/// 时间控制逻辑：
/// - 台湾宾果：每期5分钟，290秒后封盘，300秒后开奖
/// - 168飞艇：每期5分钟，230秒后封盘，240秒后开奖
///
/// 线程安全：使用ConcurrentDictionary确保多线程环境下的数据安全
/// </summary>
public static class IssueTimeHelper
{
    #region 静态属性 - 全局状态管理

    /// <summary>
    /// 当前期号信息字典
    ///
    /// 功能：按游戏类型存储当前正在进行的期号信息
    /// 键：游戏类型枚举（EnumLottery）
    /// 值：期号时间对象（IssueTime），包含期号和开奖时间
    ///
    /// 使用场景：
    /// - 主系统获取当前期号进行投注处理
    /// - 生成开奖图和路子图时确定期号
    /// - 封盘提醒和开奖处理时获取期号信息
    ///
    /// 线程安全：使用ConcurrentDictionary保证多线程访问安全
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, IssueTime> IssueTimeNowDic { get; } = new();

    /// <summary>
    /// 封盘倒计时字典
    ///
    /// 功能：按游戏类型存储距离封盘的剩余秒数
    /// 键：游戏类型枚举（EnumLottery）
    /// 值：剩余秒数（int），<=0表示已封盘
    ///
    /// 计算规则：
    /// - 台湾宾果：期号开始时间 + 290秒 - 当前时间
    /// - 168飞艇：期号开始时间 + 230秒 - 当前时间
    ///
    /// 使用场景：
    /// - 主系统判断是否允许投注（>封盘时间配置才允许）
    /// - 发送封盘提醒（<=封盘时间配置时触发）
    /// - 前端显示封盘倒计时
    ///
    /// 更新频率：每秒更新一次
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, int> CloseTimeDownDic { get; } = new();

    /// <summary>
    /// 开奖倒计时字典
    ///
    /// 功能：按游戏类型存储距离开奖的剩余秒数
    /// 键：游戏类型枚举（EnumLottery）
    /// 值：剩余秒数（int），<=0表示已开奖
    ///
    /// 计算规则：
    /// - 台湾宾果：期号开始时间 + 300秒 - 当前时间
    /// - 168飞艇：期号开始时间 + 240秒 - 当前时间
    ///
    /// 使用场景：
    /// - 主系统判断是否需要获取开奖数据
    /// - 判断是否需要结算投注订单
    /// - 前端显示开奖倒计时
    /// - 控制开奖相关业务逻辑的触发时机
    ///
    /// 更新频率：每秒更新一次
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, int> OpenTimeDownDic { get; } = new();

    #endregion

    #region 游戏配置常量 - 彩种时间规则定义

    /// <summary>
    /// 游戏时间配置字典
    ///
    /// 功能：集中管理不同彩种的时间规则配置，避免硬编码分散在各个方法中
    ///
    /// 配置说明：
    /// 1. 台湾宾果配置：
    ///    - 每日期数：203期（从第1期到第203期）
    ///    - 期号间隔：5分钟
    ///    - 开始时间：每天07:00:00
    ///    - 封盘时机：开奖前10秒（即期号开始后290秒）
    ///    - 开奖时机：期号开始后300秒
    ///    - 数据范围：当前日期前后各5天
    ///    - 期号格式：{民国年份}{6位流水号}，如：113000001
    ///
    /// 2. 168飞艇配置：
    ///    - 每日期数：180期（从第1期到第180期）
    ///    - 期号间隔：5分钟
    ///    - 开始时间：每天13:05:00
    ///    - 封盘时机：开奖前10秒（即期号开始后230秒）
    ///    - 开奖时机：期号开始后240秒
    ///    - 数据范围：整年数据（365或366天）
    ///    - 期号格式：{yyyyMMdd}{3位当日期号}，如：20241201001
    ///
    /// 设计优势：
    /// - 配置集中化：所有时间参数在一处定义，便于维护
    /// - 类型安全：使用强类型配置类，避免魔法数字
    /// - 易于扩展：新增游戏类型只需添加配置项
    /// - 代码复用：多个方法共享同一套配置
    /// </summary>
    private static readonly Dictionary<EnumLottery, GameTimeConfig> GameTimeConfigs = new()
    {
        // 台湾宾果游戏配置
        [EnumLottery.台湾宾果] = new GameTimeConfig
        {
            DailyIssueCount = 203, // 每日期数：203期
            IntervalMinutes = 5, // 期号间隔：5分钟
            StartHour = 7, // 开始小时：07时
            StartMinute = 0, // 开始分钟：00分
            CloseBeforeSeconds = 10, // 封盘提前时间：开奖前10秒 (300-290=10)
            TotalDurationSeconds = 300, // 期号总时长：300秒（5分钟）
            DateRangeBeforeDays = 5, // 生成数据范围：当前日期前5天
            DateRangeAfterDays = 5 // 生成数据范围：当前日期后5天
        },

        // 168飞艇游戏配置
        [EnumLottery.一六八飞艇] = new GameTimeConfig
        {
            DailyIssueCount = 180, // 每日期数：180期
            IntervalMinutes = 5, // 期号间隔：5分钟
            StartHour = 13, // 开始小时：13时
            StartMinute = 5, // 开始分钟：05分
            CloseBeforeSeconds = 10, // 封盘提前时间：开奖前10秒 (240-230=10)
            TotalDurationSeconds = 240, // 期号总时长：240秒（4分钟）
            DateRangeBeforeDays = 0, // 生成数据范围：不生成历史数据
            DateRangeAfterDays = 365 // 生成数据范围：整年数据
        },

        // 新一六八XL游戏配置
        [EnumLottery.新一六八XL] = new GameTimeConfig
        {
            DailyIssueCount = 180, // 每日期数：180期
            IntervalMinutes = 5, // 期号间隔：5分钟
            StartHour = 13, // 开始小时：13时
            StartMinute = 5, // 开始分钟：05分
            CloseBeforeSeconds = 10, // 封盘提前时间：开奖前10秒 (240-230=10)
            TotalDurationSeconds = 240, // 期号总时长：240秒（4分钟）
            DateRangeBeforeDays = 0, // 生成数据范围：不生成历史数据
            DateRangeAfterDays = 365 // 生成数据范围：整年数据
        }
    };

    /// <summary>
    /// 游戏时间配置类
    ///
    /// 功能：定义单个游戏类型的时间规则配置结构
    ///
    /// 属性说明：
    /// - DailyIssueCount：每日期数，决定一天生成多少个期号
    /// - IntervalMinutes：期号间隔分钟数，决定相邻期号的时间间隔
    /// - StartHour：每日开始小时，决定第一期的开奖时间（小时部分）
    /// - StartMinute：每日开始分钟，决定第一期的开奖时间（分钟部分）
    /// - CloseBeforeSeconds：封盘提前秒数，决定在开奖前多少秒封盘
    /// - TotalDurationSeconds：期号总时长秒数，决定每期的完整周期时间
    /// - DateRangeBeforeDays：生成历史数据天数，决定生成当前日期前多少天的数据
    /// - DateRangeAfterDays：生成未来数据天数，决定生成当前日期后多少天的数据
    ///
    /// 设计特点：
    /// - 使用init访问器：配置对象创建后不可修改，保证配置的稳定性
    /// - 类型安全：所有属性都有明确的类型定义
    /// - 语义清晰：属性名称直观表达其用途
    /// </summary>
    private class GameTimeConfig
    {
        /// <summary>每日期数</summary>
        public int DailyIssueCount { get; init; }

        /// <summary>期号间隔分钟数</summary>
        public int IntervalMinutes { get; init; }

        /// <summary>每日开始小时</summary>
        public int StartHour { get; init; }

        /// <summary>每日开始分钟</summary>
        public int StartMinute { get; init; }

        /// <summary>封盘提前秒数</summary>
        public int CloseBeforeSeconds { get; init; }

        /// <summary>期号总时长秒数</summary>
        public int TotalDurationSeconds { get; init; }

        /// <summary>生成历史数据天数</summary>
        public int DateRangeBeforeDays { get; init; }

        /// <summary>生成未来数据天数</summary>
        public int DateRangeAfterDays { get; init; }
    }

    #endregion

    #region 公共方法 - 对外提供的核心接口

    /// <summary>
    /// 生成期号时间信息
    ///
    /// 功能：根据不同彩种规则生成期号和开奖时间，并写入数据库
    ///
    /// 执行流程：
    /// 1. 初始化当前期号信息字典，为后续监控做准备
    /// 2. 清空数据库中的现有期号时间数据，避免重复数据
    /// 3. 根据当前游戏类型调用对应的期号生成方法
    /// 4. 将生成的期号时间数据批量插入数据库
    /// 5. 记录操作日志，便于问题追踪
    ///
    /// 调用时机：
    /// - 系统启动时调用，初始化期号时间数据
    /// - 需要重新生成期号数据时调用
    ///
    /// 数据生成规则：
    /// - 台湾宾果：生成当前日期前后各5天的期号数据，每天203期
    /// - 168飞艇：生成整年的期号数据，每天180期
    ///
    /// 异常处理：
    /// - 捕获所有异常并记录到日志
    /// - 重新抛出异常，让调用方感知错误
    ///
    /// 性能考虑：
    /// - 使用批量插入提高数据库操作效率
    /// - 异步操作避免阻塞主线程
    /// </summary>
    /// <param name="token">取消令牌，用于支持操作取消</param>
    /// <returns>异步任务</returns>
    /// <exception cref="NotSupportedException">当游戏类型不受支持时抛出</exception>
    /// <exception cref="Exception">数据库操作或其他异常</exception>
    public static async Task CreateIssueTimeAsync(CancellationToken token)
    {
        try
        {
            // 初始化当前期号信息字典
            // 为每个游戏类型创建一个空的期号对象，避免后续访问时出现KeyNotFoundException
            IssueTimeNowDic.TryAdd(CommonHelper.Lottery, new IssueTime());

            // 清空现有数据，确保数据的一致性
            // 删除数据库中所有现有的期号时间记录，避免新旧数据混合
            await ClearExistingIssueTimeData(token);

            // 根据游戏类型生成期号时间数据
            // 使用switch表达式根据当前游戏类型调用对应的生成方法
            var issueTimeList = CommonHelper.Lottery switch
            {
                EnumLottery.台湾宾果 => await GenerateTaiwanBingoIssueTimesAsync(),
                EnumLottery.一六八飞艇 => await GenerateAircraftIssueTimesAsync(),
                EnumLottery.新一六八XL => await GenerateAircraftIssueTimesAsync(),
                _ => throw new NotSupportedException($"不支持的游戏类型: {CommonHelper.Lottery}")
            };

            // 批量插入数据到数据库
            // 只有在生成了数据的情况下才执行插入操作，避免无意义的数据库调用
            if (issueTimeList.Count > 0)
            {
                // 使用FreeSql的批量插入功能，提高插入效率
                await DbHelper.FSql.Insert(issueTimeList).ExecuteAffrowsAsync(token);

                // 记录成功日志，包含生成的数据条数和游戏类型信息
                await DbHelper.AddLog(EnumLogType.机器人, "生成期号时间",
                    $"成功生成 {issueTimeList.Count} 条 {CommonHelper.Lottery} 期号时间数据");
            }
        }
        catch (Exception ex)
        {
            // 记录异常日志，包含完整的异常信息
            await DbHelper.AddLog(EnumLogType.机器人, "CreateIssueTimeAsync异常", ex.ToString());

            // 重新抛出异常，让调用方能够感知到错误并进行相应处理
            throw;
        }
    }

    /// <summary>
    /// 循环处理开奖期号时间监控
    ///
    /// 功能：这是系统的时间监控核心，负责实时跟踪当前期号状态和倒计时
    ///
    /// 执行流程：
    /// 1. 初始化倒计时字典，设置初始值为0
    /// 2. 获取当前游戏类型的时间配置
    /// 3. 进入无限循环，每秒执行一次监控逻辑
    /// 4. 更新当前期号信息（仅在倒计时结束时重新查询）
    /// 5. 更新封盘和开奖倒计时（每秒都更新）
    ///
    /// 监控逻辑：
    /// - 当前期号确定：通过数据库查询找到当前时间对应的期号
    /// - 倒计时计算：基于期号开始时间和游戏配置计算剩余时间
    /// - 状态更新：实时更新三个核心字典的数据
    ///
    /// 性能优化：
    /// - 只在倒计时结束时才重新查询数据库，减少数据库压力
    /// - 使用1秒间隔，平衡实时性和性能
    /// - 异步操作避免阻塞其他业务逻辑
    ///
    /// 异常处理：
    /// - 捕获所有异常并记录日志
    /// - 不重新抛出异常，确保监控循环不会因异常而中断
    ///
    /// 调用时机：
    /// - 系统启动后立即调用，作为后台服务运行
    /// - 整个系统运行期间持续执行，直到系统关闭
    ///
    /// 依赖关系：
    /// - 依赖CreateIssueTimeAsync先生成期号数据
    /// - 为主系统的投注、封盘、开奖等业务提供时间基础
    /// </summary>
    /// <param name="token">取消令牌，用于优雅停止监控循环</param>
    /// <returns>异步任务，循环执行直到取消</returns>
    public static async Task PreIssueTimeAsync(CancellationToken token)
    {
        try
        {
            // 初始化倒计时字典
            // 为当前游戏类型设置初始倒计时值为0，避免后续访问时出现KeyNotFoundException
            CloseTimeDownDic.TryAdd(CommonHelper.Lottery, 0);
            OpenTimeDownDic.TryAdd(CommonHelper.Lottery, 0);

            // 获取当前游戏配置
            // 从配置字典中获取当前游戏类型的时间规则配置
            var config = GameTimeConfigs[CommonHelper.Lottery];

            // 主监控循环
            // 持续运行直到收到取消信号，每秒执行一次监控逻辑
            while (!token.IsCancellationRequested)
            {
                // 等待1秒，控制监控频率
                // 1秒的间隔既保证了实时性，又避免了过度消耗CPU资源
                await Task.Delay(1000, token);

                // 更新当前期号信息
                // 根据当前时间和游戏配置，确定当前应该进行的期号
                // 只有在倒计时都结束时才重新查询，优化性能
                await UpdateCurrentIssueInfo(token);

                // 更新倒计时
                // 基于当前期号的开始时间和游戏配置，计算封盘和开奖的剩余时间
                // 每次循环都会更新，确保倒计时的准确性
                UpdateCountdownTimers();
            }
        }
        catch (Exception ex)
        {
            // 记录异常日志
            // 监控循环中的异常不应该中断整个系统，所以只记录日志不重新抛出
            await DbHelper.AddLog(EnumLogType.机器人, "PreIssueTimeAsync异常", ex.ToString());
        }
    }

    #endregion

    #region 私有方法 - 内部实现逻辑

    /// <summary>
    /// 清空现有期号时间数据
    ///
    /// 功能：删除数据库中所有现有的期号时间记录
    ///
    /// 执行逻辑：
    /// - 删除IssueTime表中所有Issue字段不为空的记录
    /// - 使用异步操作避免阻塞主线程
    /// - 支持取消令牌，允许操作被中断
    ///
    /// 调用时机：
    /// - 在生成新的期号数据之前调用
    /// - 确保数据库中没有旧的期号数据残留
    ///
    /// 设计考虑：
    /// - 使用条件删除而不是全表删除，更加安全
    /// - 异步操作提高系统响应性
    /// </summary>
    /// <param name="token">取消令牌</param>
    /// <returns>异步任务</returns>
    private static async Task ClearExistingIssueTimeData(CancellationToken token)
    {
        // 删除所有有效的期号时间记录
        // 条件：Issue字段不为空，避免删除无效记录
        await DbHelper.FSql.Delete<IssueTime>()
            .Where(a => !string.IsNullOrEmpty(a.Issue))
            .ExecuteAffrowsAsync(token);
    }

    /// <summary>
    /// 生成台湾宾果期号时间数据
    ///
    /// 功能：根据台湾宾果的特殊规则生成期号和开奖时间数据
    ///
    /// 期号格式：{民国年份}{6位流水号}
    /// 示例：113000001（民国113年第1期）
    ///
    /// 时间安排：
    /// - 每天203期，从07:00:00开始
    /// - 每期间隔5分钟
    /// - 生成范围：当前日期前后各5天
    ///
    /// 关键算法：
    /// 1. 民国年份计算：公历年份 - 1911
    /// 2. 流水号生成：从年初开始连续计算，确保期号的唯一性和连续性
    /// 3. 6位流水号格式：使用"000000"补齐前导零
    ///
    /// 重要特性：
    /// - 必须按照原始逻辑，从年初开始计算流水号
    /// - 即使某些日期不在生成范围内，也要递增流水号以保持连续性
    /// - 这样确保了期号的全局唯一性和时间顺序性
    ///
    /// 执行流程：
    /// 1. 获取台湾宾果的游戏配置
    /// 2. 按年、月、日的顺序遍历时间
    /// 3. 对于每一天，判断是否在生成范围内
    /// 4. 在范围内的日期生成具体的期号数据
    /// 5. 不在范围内的日期仍然递增流水号计数器
    ///
    /// 性能考虑：
    /// - 只生成指定日期范围内的数据，避免生成过多无用数据
    /// - 使用List<IssueTime>批量收集数据，提高内存效率
    /// </summary>
    /// <returns>台湾宾果期号时间数据列表</returns>
    private static async Task<List<IssueTime>> GenerateTaiwanBingoIssueTimesAsync()
    {
        // 获取台湾宾果的游戏配置
        var config = GameTimeConfigs[EnumLottery.台湾宾果];
        var dataList = new List<IssueTime>();
        var currentDate = DateTime.Now;

        // 按照原始逻辑：遍历年份，从年初开始计算流水号
        // 这里flagTime和endTime都设为当前日期，表示只处理当前年份
        DateTime flagTime = currentDate;
        DateTime endTime = currentDate;
        int issueIndex = 0; // 流水号计数器，从0开始

        // 遍历年份（原始代码逻辑）
        // 虽然这里只处理当前年份，但保持原始的循环结构以确保逻辑一致性
        while (flagTime.Year <= endTime.Year)
        {
            // 遍历月份（1-12月）
            for (int month = 1; month <= 12; month++)
            {
                // 遍历天数（1-当月最大天数）
                for (int dayIndex = 1; dayIndex <= DateTime.DaysInMonth(flagTime.Year, month); dayIndex++)
                {
                    var currentDay = new DateTime(flagTime.Year, month, dayIndex);

                    // 判断当前日期是否在生成范围内
                    // 生成范围：当前日期前后各5天
                    if (currentDay.Date >= currentDate.AddDays(-config.DateRangeBeforeDays).Date &&
                        currentDay.Date <= currentDate.AddDays(config.DateRangeAfterDays).Date)
                    {
                        // 生成当天的所有期号数据
                        // 调用专门的方法生成单日的期号，并传递流水号计数器的引用
                        var dailyIssues = GenerateDailyTaiwanBingoIssuesWithContinuousIndex(
                            currentDay, config, ref issueIndex);
                        dataList.AddRange(dailyIssues);
                    }
                    else
                    {
                        // 即使不在生成范围内，也要递增issueIndex以保持流水号连续性
                        // 这是台湾宾果期号生成的关键逻辑：确保期号的全局唯一性
                        issueIndex += config.DailyIssueCount;
                    }
                }
            }

            // 移动到下一年（虽然在当前逻辑中不会执行，但保持原始结构）
            flagTime = flagTime.AddYears(1);
        }

        // 保持异步签名一致性，虽然这里没有真正的异步操作
        await Task.CompletedTask;
        return dataList;
    }

    /// <summary>
    /// 生成168飞艇期号时间数据
    ///
    /// 功能：根据168飞艇的规则生成期号和开奖时间数据
    ///
    /// 期号格式：{yyyyMMdd}{3位当日期号}
    /// 示例：20241201001（2024年12月1日第1期）
    ///
    /// 时间安排：
    /// - 每天180期，从13:05:00开始
    /// - 每期间隔5分钟
    /// - 生成范围：整年数据（365或366天）
    ///
    /// 关键算法：
    /// 1. 日期格式：yyyyMMdd（如：20241201）
    /// 2. 当日期号：001-180，固定3位数字
    /// 3. 期号递增：使用数值递增而非字符串拼接
    ///
    /// 与台湾宾果的区别：
    /// - 期号格式不同：日期+当日期号 vs 民国年份+流水号
    /// - 生成范围不同：整年 vs 当前日期前后各5天
    /// - 期号逻辑不同：每日重新开始 vs 全年连续流水号
    ///
    /// 执行流程：
    /// 1. 获取168飞艇的游戏配置
    /// 2. 计算当前年份的总天数（考虑闰年）
    /// 3. 遍历全年每一天
    /// 4. 为每一天生成180个期号
    /// 5. 收集所有期号数据并返回
    ///
    /// 性能考虑：
    /// - 一次性生成整年数据，减少频繁的数据生成操作
    /// - 使用DateTime.IsLeapYear()准确计算年份天数
    /// </summary>
    /// <returns>168飞艇期号时间数据列表</returns>
    private static async Task<List<IssueTime>> GenerateAircraftIssueTimesAsync()
    {
        // 获取168飞艇的游戏配置
        var config = GameTimeConfigs[EnumLottery.一六八飞艇];
        var dataList = new List<IssueTime>();
        var currentYear = DateTime.Now.Year;
        var startDate = new DateTime(currentYear, 1, 1); // 从年初开始

        // 计算当前年份的总天数，考虑闰年情况
        var dayCount = DateTime.IsLeapYear(currentYear) ? 366 : 365;

        // 遍历全年每一天
        for (int dayIndex = 0; dayIndex < dayCount; dayIndex++)
        {
            // 计算当前日期
            var currentDate = startDate.AddDays(dayIndex);

            // 生成当天的所有期号数据
            var dailyIssues = GenerateDailyAircraftIssues(currentDate, config);
            dataList.AddRange(dailyIssues);
        }

        // 保持异步签名一致性，虽然这里没有真正的异步操作
        await Task.CompletedTask;
        return dataList;
    }

    /// <summary>
    /// 生成台湾宾果单日期号数据（保持流水号连续性）
    ///
    /// 功能：为指定日期生成台湾宾果的所有期号数据，确保流水号的连续性
    ///
    /// 核心算法：
    /// 1. 民国年份转换：公历年份 - 1911
    /// 2. 6位流水号格式：使用"000000"字符串截取方式补齐前导零
    /// 3. 时间递增：每期在前一期基础上增加5分钟
    ///
    /// 期号生成逻辑：
    /// - 首期：当天07:00:00开始
    /// - 后续期号：在前一期时间基础上增加5分钟
    /// - 期号格式：{民国年份} + {6位流水号}
    ///
    /// 流水号连续性：
    /// - 使用ref参数传递流水号计数器
    /// - 确保跨日期的流水号连续性
    /// - 这是台湾宾果期号系统的关键特性
    ///
    /// 原始格式兼容：
    /// - 使用"000000".Substring()方式生成前导零
    /// - 完全按照原始代码的字符串处理逻辑
    /// - 确保生成的期号格式与历史数据一致
    ///
    /// 执行流程：
    /// 1. 计算民国年份
    /// 2. 生成当天首期（07:00:00）
    /// 3. 循环生成第2-203期，每期递增5分钟
    /// 4. 每生成一期都递增流水号计数器
    /// </summary>
    /// <param name="date">要生成期号的日期</param>
    /// <param name="config">游戏时间配置</param>
    /// <param name="issueIndex">流水号计数器（引用传递，确保连续性）</param>
    /// <returns>当天的所有期号数据</returns>
    private static List<IssueTime> GenerateDailyTaiwanBingoIssuesWithContinuousIndex(DateTime date, GameTimeConfig config, ref int issueIndex)
    {
        var dailyIssues = new List<IssueTime>();
        var mingGuoYear = date.Year - 1911; // 转换为民国年份

        // 生成当天首期
        issueIndex++; // 递增流水号计数器
        var firstIssueTime = new DateTime(date.Year, date.Month, date.Day, config.StartHour, config.StartMinute, 0);

        // 使用原始的字符串处理方式生成6位流水号
        // "000000".Substring(0, 6 - issueIndex.ToString().Length) 用于生成前导零
        var firstIssueNumber = mingGuoYear + "000000".Substring(0, 6 - issueIndex.ToString().Length) + issueIndex;

        dailyIssues.Add(new IssueTime
        {
            Issue = firstIssueNumber,
            OpenTime = firstIssueTime
        });

        // 生成第2至203期（按照原始逻辑）
        var currentIssueTime = firstIssueTime;
        for (int periodIndex = 2; periodIndex <= config.DailyIssueCount; periodIndex++)
        {
            issueIndex++; // 递增流水号计数器
            currentIssueTime = currentIssueTime.AddMinutes(config.IntervalMinutes); // 时间递增5分钟

            // 生成期号：民国年份 + 6位流水号（使用原始格式）
            var issueNumber = mingGuoYear + "000000".Substring(0, 6 - issueIndex.ToString().Length) + issueIndex;

            dailyIssues.Add(new IssueTime
            {
                Issue = issueNumber,
                OpenTime = currentIssueTime
            });
        }

        return dailyIssues;
    }

    /// <summary>
    /// 生成168飞艇单日期号数据
    ///
    /// 功能：为指定日期生成168飞艇的所有期号数据
    ///
    /// 核心算法：
    /// 1. 日期格式化：使用"yyyyMMdd"格式
    /// 2. 首期固定：每天第一期固定为"001"
    /// 3. 期号递增：使用数值转换方式递增期号
    ///
    /// 期号生成逻辑：
    /// - 首期：当天13:05:00开始，期号为{yyyyMMdd}001
    /// - 后续期号：时间递增5分钟，期号数值递增1
    /// - 期号范围：001-180（每天180期）
    ///
    /// 与台湾宾果的区别：
    /// - 每日独立：每天的期号都从001开始，不跨日连续
    /// - 数值递增：使用Convert.ToInt64()进行数值运算
    /// - 格式简单：日期+当日序号，无需复杂的流水号逻辑
    ///
    /// 原始逻辑兼容：
    /// - 使用Convert.ToInt64()和ToString()的转换方式
    /// - 完全按照原始代码的期号递增逻辑
    /// - 确保生成的期号与历史数据格式一致
    ///
    /// 执行流程：
    /// 1. 生成当天首期（13:05:00，期号001）
    /// 2. 循环生成第2-180期
    /// 3. 每期时间递增5分钟，期号数值递增1
    /// 4. 收集所有期号数据并返回
    /// </summary>
    /// <param name="date">要生成期号的日期</param>
    /// <param name="config">游戏时间配置</param>
    /// <returns>当天的所有期号数据</returns>
    private static List<IssueTime> GenerateDailyAircraftIssues(DateTime date, GameTimeConfig config)
    {
        var dailyIssues = new List<IssueTime>();

        // 生成当天首期开奖期号和时间（按照原始逻辑）
        var firstIssueTime = new DateTime(date.Year, date.Month, date.Day, config.StartHour, config.StartMinute, 0);
        var firstIssueNumber = date.ToString("yyyyMMdd") + "001"; // 首期固定为001

        dailyIssues.Add(new IssueTime
        {
            Issue = firstIssueNumber,
            OpenTime = firstIssueTime
        });

        // 遍历生成第2至180期（按照原始逻辑）
        var currentIssueTime = firstIssueTime;
        var currentIssueNumber = firstIssueNumber;

        for (int periodIndex = 2; periodIndex <= config.DailyIssueCount; periodIndex++)
        {
            // 时间递增5分钟
            currentIssueTime = currentIssueTime.AddMinutes(config.IntervalMinutes);

            // 期号数值递增（原始逻辑：转换为数值+1再转回字符串）
            currentIssueNumber = (Convert.ToInt64(currentIssueNumber) + 1).ToString();

            dailyIssues.Add(new IssueTime
            {
                Issue = currentIssueNumber,
                OpenTime = currentIssueTime
            });
        }

        return dailyIssues;
    }

    /// <summary>
    /// 更新当前期号信息（完全按照原始逻辑）
    /// 
    /// 功能：根据当前时间确定当前应该进行的期号
    /// 
    /// 执行条件：
    /// - 只有在封盘倒计时和开奖倒计时都<=0时才重新查询
    /// - 这样避免了频繁的数据库查询，优化性能
    /// 
    /// 查询逻辑：
    /// 1. 首先查找当前时间范围内正在进行的期号
    /// 2. 如果没有找到，则查找下一个即将开始的期号
    /// 3. 时间范围根据游戏类型确定（台湾宾果300秒，168飞艇240秒）
    /// 
    /// 原始逻辑兼容：
    /// - 完全按照原始代码的查询条件和逻辑
    /// - 使用相同的数据库查询语句
    /// - 保持相同的异常处理方式
    /// 
    /// 性能优化：
    /// - 只在必要时查询数据库
    /// - 使用异步查询避免阻塞
    /// - 支持取消令牌
    /// </summary>
    /// <param name="token">取消令牌</param>
    /// <returns>异步任务</returns>
    private static async Task UpdateCurrentIssueInfo(CancellationToken token)
    {
        // 只有在倒计时都结束时才重新查询当前期号（原始逻辑）
        // 这个条件确保了只在期号切换时才查询数据库，避免频繁查询
        if (CloseTimeDownDic[CommonHelper.Lottery] <= 0 && OpenTimeDownDic[CommonHelper.Lottery] <= 0)
        {
            // 确定当前进行的期号（原始逻辑）
            DateTime now = DateTime.Now;

            // 根据游戏类型确定期号的有效时间范围
            // 台湾宾果：300秒（5分钟），168飞艇：240秒（4分钟）
            int addSeconds = CommonHelper.Lottery.Equals(EnumLottery.台湾宾果) ? 300 : 240;

            // 查询当前时间范围内的期号，如果没有则查询下一个期号
            IssueTimeNowDic[CommonHelper.Lottery] = await DbHelper.FSql.Select<IssueTime>()
                                                        .Where(a => now >= a.OpenTime && now <= a.OpenTime.AddSeconds(addSeconds))
                                                        .ToOneAsync(token) ??
                                                    (await DbHelper.FSql.Select<IssueTime>()
                                                        .Where(a => now <= a.OpenTime)
                                                        .ToListAsync(token)).First();
        }
    }

    /// <summary>
    /// 更新倒计时（完全按照原始逻辑）
    /// 
    /// 功能：计算并更新封盘和开奖的倒计时
    /// 
    /// 计算规则：
    /// - 台湾宾果：封盘290秒后，开奖300秒后
    /// - 168飞艇：封盘230秒后，开奖240秒后
    /// 
    /// 计算方式：
    /// - 基于当前期号的开始时间
    /// - 加上对应的秒数后减去当前时间
    /// - 转换为整数秒数存储
    /// 
    /// 原始逻辑兼容：
    /// - 使用相同的switch语句结构
    /// - 使用相同的时间计算方式
    /// - 保持相同的数据类型转换
    /// 
    /// 实时更新：
    /// - 每秒调用一次，确保倒计时的准确性
    /// - 直接操作全局字典，供其他模块使用
    /// - 负数倒计时表示已经超时
    /// 
    /// 使用场景：
    /// - 主系统判断是否允许投注
    /// - 触发封盘和开奖相关业务逻辑
    /// - 前端显示倒计时信息
    /// </summary>
    private static void UpdateCountdownTimers()
    {
        // 确定当前进行期的时间（原始逻辑）
        // 使用switch语句根据游戏类型计算不同的倒计时
        switch (CommonHelper.Lottery)
        {
            case EnumLottery.台湾宾果:
                // 台湾宾果倒计时计算
                // 封盘时间：期号开始时间 + 290秒 - 当前时间
                CloseTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.台湾宾果].OpenTime.AddSeconds(290.0).Subtract(DateTime.Now).TotalSeconds;
                // 开奖时间：期号开始时间 + 300秒 - 当前时间
                OpenTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.台湾宾果].OpenTime.AddSeconds(300.0).Subtract(DateTime.Now).TotalSeconds;
                break;

            case EnumLottery.一六八飞艇:
                // 168飞艇倒计时计算
                // 封盘时间：期号开始时间 + 230秒 - 当前时间
                CloseTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.一六八飞艇].OpenTime.AddSeconds(230.0).Subtract(DateTime.Now).TotalSeconds;
                // 开奖时间：期号开始时间 + 240秒 - 当前时间
                OpenTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.一六八飞艇].OpenTime.AddSeconds(240.0).Subtract(DateTime.Now).TotalSeconds;
                break;

            case EnumLottery.新一六八XL:
                // 168飞艇倒计时计算
                // 封盘时间：期号开始时间 + 230秒 - 当前时间
                CloseTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.新一六八XL].OpenTime.AddSeconds(230.0).Subtract(DateTime.Now).TotalSeconds;
                // 开奖时间：期号开始时间 + 240秒 - 当前时间
                OpenTimeDownDic[CommonHelper.Lottery] = (int)IssueTimeNowDic[EnumLottery.新一六八XL].OpenTime.AddSeconds(240.0).Subtract(DateTime.Now).TotalSeconds;
                break;
        }
    }

    #endregion
}