﻿using AiHelper;
using Robot.Config;
using Robot.Constants;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

/// <summary>
/// 显示机器人信息
/// </summary>
public partial class FormMain
{
    #region 基础信息

    /// <summary>
    /// 基础信息
    /// </summary>
    private async Task ShowInfoAsync()
    {
        try
        {
            // 头部标题及右下角信息
            Invoke(() =>
            {
                Text = $@"{Setting.Current.AppName} - Ver:{Ai.中括号左}{CommonHelper.AppVersion}{Ai.中括号右} - 服务到期时间:{Ai.中括号左}{RegisterHelper.MyRegisterInfo.ExpireTime:yyyy-MM-dd HH:mm:ss}{Ai.中括号右}";
                // toolStripStatusLabel_Lottery.Text = $@"当前游戏:{Ai.中括号左}{CommonHelper.PlayGameInfo.GameName}{Ai.中括号右}";
                toolStripStatusLabel_RobotInfo.Text = $@"机器人:{Ai.中括号左}{RobotHelper.RobotInfo.Account}{Ai.小圆点}{RobotHelper.RobotInfo.NickName}{Ai.中括号右}";
            });

            // 显示封盘时间
            if (!string.IsNullOrWhiteSpace(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                Invoke(() =>
                {
                    toolStripStatusLabel_CloseTime.Text = string.Concat($"{Ai.中括号左}{CommonHelper.Lottery}{Ai.中括号右}@ ", IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, " 期距离封盘:", Ai.中括号左, IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery], Ai.中括号右);
                    toolStripStatusLabel_OpenTime.Text = string.Concat("距离下期:", Ai.中括号左, IssueTimeHelper.OpenTimeDownDic[CommonHelper.Lottery], Ai.中括号右);
                });
            }

            // 显示头像
            // if (!string.IsNullOrEmpty(RobotHelper.RobotInfo.AvatarUrl))
            // {
            //     Invoke(() => { uiAvatar_Robot.Image = RobotHelper.RobotInfo.Avatar; });
            // }

            // 判断显示最新kj信息
            // KaiJiang lastKj = await DbHelper.FSql.Select<KaiJiang>()
            //     .OrderByDescending(a => a.Issue)
            //     .FirstAsync();
            // if (lastKj != null && !string.IsNullOrEmpty(lastKj.Issue))
            // {
            //     // Invoke(() => { toolStripStatusLabel_OpenData.Text = $@"{lastKj.Issue} 期号码:{Ai.中括号左}{Ai.中括号右}=>番摊{Ai.中括号左}{Ai.中括号右}"; });
            // }

            // 加载群列表
            Invoke(() =>
            {
                if (comboBox_WorkGroupId.Items.Count.Equals(0) && RobotHelper.GroupDic.Any())
                {
                    comboBox_WorkGroupId.Items.Add("选择聊天群");
                    foreach (KeyValuePair<string, string> kv in RobotHelper.GroupDic)
                    {
                        comboBox_WorkGroupId.Items.Add($"{Ai.中括号左}{kv.Key}{Ai.中括号右}{kv.Value}");
                    }

                    comboBox_WorkGroupId.SelectedIndex = 0;
                }
            });

            // 判断显示飞单额度信息
            Invoke(() =>
            {
                if (!string.IsNullOrEmpty(CommonHelper.UserInfo.UserName))
                {
                    toolStripStatusLabel_MemberInfo.Visible = true;
                    toolStripStatusLabel_MemberInfo.Text = $@"{Ai.中括号左}{CommonHelper.UserInfo.UserName}{Ai.中括号右}额度:{Ai.中括号左}{CommonHelper.UserInfo.Balance}{Ai.中括号右}";
                }
                else
                {
                    toolStripStatusLabel_MemberInfo.Visible = false;
                }
            });

            // 显示飞单结果
            if (CommonHelper.BetSuccessIssueDic.ContainsKey(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue) && CommonHelper.BetSuccessIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue].Equals(false))
            {
                CommonHelper.BetSuccessIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue] = true;
                // Invoke(() => { this.ShowSuccessNotifier($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期飞单成功。", timeout: 10000); });
            }
            else if (CommonHelper.BetFailIssueDic.ContainsKey(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue) && CommonHelper.BetFailIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue].Equals(false))
            {
                CommonHelper.BetFailIssueDic[IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue] = true;
                // Invoke(() => { this.ShowErrorNotifier($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期飞单可能失败，请人工核查结果。", timeout: 10000); });
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ShowInfoAsyncError", ex.ToString());
        }
    }

    #endregion

    #region 显示会员信息

    private decimal MemberTotalBalance { get; set; }

    /// <summary>
    /// 加载Member信息
    /// </summary>
    private void ShowMemberInfoAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 所有会员列表
                List<Member> memberListAll = await DbHelper.FSql.Select<Member>().ToListAsync();

                // 没有数据直接返回
                if (!memberListAll.Any())
                {
                    if (dataGridView_MemberInfo.Rows.Count > 0)
                    {
                        dataGridView_MemberInfo.DataSource = null;
                    }

                    return;
                }

                // 计算总计数据
                decimal tmpTotalBalance = memberListAll.Sum(a => a.Balance);

                // 对比数据差异
                if (MemberTotalBalance.Equals(tmpTotalBalance) && !CommonHelper.NeedToRefreshMemberInfo)
                {
                    return;
                }

                // 重置状态
                MemberTotalBalance = tmpTotalBalance;
                CommonHelper.NeedToRefreshMemberInfo = false;

                // 定义临时数据源格式
                List<MemberInfo> memberInfoList = new();
                foreach (Member member in memberListAll)
                {
                    MemberInfo info = RobotHelper.GetMemberDetail(member).Result;
                    memberInfoList.Add(info);
                }

                // 将memberInfoList按照积分从高到低排序
                memberInfoList.Sort((a, b) => b.积分.CompareTo(a.积分));

                // 插入汇总数据
                memberInfoList.Insert(0, new MemberInfo
                {
                    账号 = "汇总",
                    备注名 = "数据",
                    积分 = Math.Round(memberInfoList.Sum(a => a.积分), 2),
                    本期下注 = Math.Round(memberInfoList.Sum(a => a.本期下注), 2),
                    总流水 = Math.Round(memberInfoList.Sum(a => a.总流水), 2),
                    总盈亏 = Math.Round(memberInfoList.Sum(a => a.总盈亏), 2),
                    未回流水 = Math.Round(memberInfoList.Sum(a => a.未回流水), 2),
                    已回流水 = Math.Round(memberInfoList.Sum(a => a.已回流水), 2),
                    回水比例 = Math.Round(Setting.Current.回水比例, 2),
                    已回金额 = Math.Round(memberInfoList.Sum(a => a.已回金额), 2),
                    总上分 = Math.Round(memberInfoList.Sum(a => a.总上分), 2),
                    总下分 = Math.Round(memberInfoList.Sum(a => a.总下分), 2),
                    真流水 = Math.Round(memberInfoList.Sum(a => a.真流水), 2),
                    真盈亏 = Math.Round(memberInfoList.Sum(a => a.真盈亏), 2),
                    假流水 = Math.Round(memberInfoList.Sum(a => a.假流水), 2),
                    假盈亏 = Math.Round(memberInfoList.Sum(a => a.假盈亏), 2),
                });

                // 绑定明细表格到表格
                DataGridView dgv = dataGridView_MemberInfo;
                dgv.DataSource = null;
                dgv.DataSource = memberInfoList;

                // 设置表格样式
                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.Columns["账号"]!.Width = 135;
                    dgv.Columns["备注名"]!.Width = 100;
                    dgv.Columns["积分"]!.Width = 80;
                    dgv.Columns["本期下注"]!.Width = 80;
                    dgv.Columns["总流水"]!.Width = 80;
                    dgv.Columns["总盈亏"]!.Width = 80;
                    dgv.Columns["未回流水"]!.Width = 80;
                    dgv.Columns["已回流水"]!.Width = 80;
                    dgv.Columns["回水比例"]!.Width = 80;
                    dgv.Columns["已回金额"]!.Width = 80;
                    dgv.Columns["总上分"]!.Width = 80;
                    dgv.Columns["总下分"]!.Width = 80;
                    dgv.Columns["真流水"]!.Width = 80;
                    dgv.Columns["真盈亏"]!.Width = 80;
                    dgv.Columns["假流水"]!.Width = 80;
                    dgv.Columns["假盈亏"]!.Width = 80;

                    // 设置对齐方式
                    dgv.Columns["账号"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["备注名"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["积分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["本期下注"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["未回流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["已回流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["回水比例"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dgv.Columns["已回金额"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总上分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["总下分"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["真流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["真盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["假流水"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns["假盈亏"]!.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

                    // 取消选中任何单元格
                    dgv.ClearSelection();

                    // 固定第1列和第2列
                    dgv.Columns[0].Frozen = dgv.Columns[1].Frozen = true;
                }
            }
            catch (Exception ex)
            {
                // Debug.WriteLine(ex);
                await DbHelper.AddLog(EnumLogType.机器人, "ShowMemberInfoAsyncError", ex.ToString());
            }
        });
    }

    #endregion

    #region 显示答题明细数据

    private List<long> BetOrderIdList { get; set; } = new();

    /// <summary>
    /// 答题数据
    /// </summary>
    private void ShowBetOrderAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 提取答题数据
                var betOrderList = await DbHelper.FSql.Select<BetOrder, Member>()
                    .InnerJoin(t => t.t1.Account == t.t2.Account)
                    .Where(t => t.t1.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where(t => t.t1.BetOrderStatus == EnumBetOrderStatus.已受理)
                    .ToListAsync(t => new
                    {
                        t.t1.Id,
                        t.t1.BetLottery,
                        期号 = t.t1.Issue,
                        t.t2.Account,
                        t.t2.备注名,
                        项目 = t.t1.Con,
                        赔率 = t.t1.Odds,
                        金额 = t.t1.Money
                    });

                // 没有数据直接返回
                if (!betOrderList.Any())
                {
                    try
                    {
                        dataGridView_BetOrderDetail.Rows.Clear();
                    }
                    catch (Exception ex)
                    {
                        // Debug.WriteLine(ex);
                        await DbHelper.AddLog(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                    }

                    return;
                }

                // 逐行添加数据
                bool needRefresh = false;
                foreach (var order in betOrderList)
                {
                    if (!BetOrderIdList.Contains(order.Id))
                    {
                        BetOrderIdList.Add(order.Id);

                        try
                        {
                            dataGridView_BetOrderDetail.Rows.Add(
                                order.Account,
                                order.备注名,
                                order.BetLottery,
                                order.期号,
                                order.项目,
                                order.赔率,
                                order.金额);
                        }
                        catch (Exception ex)
                        {
                            // Debug.WriteLine(ex);
                            await DbHelper.AddLog(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                        }

                        needRefresh = true;
                    }
                }

                // 显示到最后一行
                if (needRefresh)
                {
                    try
                    {
                        // 取消选中第一行
                        dataGridView_BetOrderDetail.Rows[0].Selected = false;

                        // 显示最后一行
                        dataGridView_BetOrderDetail.FirstDisplayedScrollingRowIndex = dataGridView_BetOrderDetail.RowCount - 1;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLog(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                // Debug.WriteLine(ex.ToString());
                await DbHelper.AddLog(EnumLogType.机器人, "ShowBetDataAsync", ex.ToString());
            }
        });
    }

    #endregion

    #region 显示上分订单数据

    private List<long> AddMoneyOrderIdList { get; set; } = new();

    /// <summary>
    /// 上分订单数据
    /// </summary>
    private async void ShowAddMoneyOrderAsync()
    {
        try
        {
            var addMoneyOrderList = await DbHelper.FSql.Select<AddMoney, Member>()
                .InnerJoin(t => t.t1.Account == t.t2.Account)
                .Where(t => t.t1.Status == EnumBalanceStatus.等待处理)
                .ToListAsync(t => new
                {
                    t.t1.Id,
                    t.t1.Time,
                    t.t1.Account,
                    t.t2.备注名,
                    t.t1.Money,
                    t.t1.FromMsgId
                });

            // 对比数据差异
            foreach (var order in addMoneyOrderList)
            {
                if (!AddMoneyOrderIdList.Contains(order.Id))
                {
                    AddMoneyOrderIdList.Add(order.Id);
                    Invoke(() => { dataGridView_AddMoney.Rows.Add(order.Id, order.Account, order.备注名, order.Money, "同意", "拒绝"); });
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ShowAddMoneyOrderAsyncError", ex.ToString());
        }
    }

    #endregion

    #region 显示下分订单数据

    private List<long> SubMoneyOrderIdList { get; set; } = new();

    /// <summary>
    /// 下分订单数据
    /// </summary>
    private async void ShowSubMoneyOrderAsync()
    {
        try
        {
            var subMoneyOrderList = await DbHelper.FSql.Select<SubMoney, Member>()
                .InnerJoin(t => t.t1.Account == t.t2.Account)
                .Where(t => t.t1.Status == EnumBalanceStatus.等待处理)
                .ToListAsync(t => new
                {
                    t.t1.Id,
                    t.t1.Time,
                    t.t1.Account,
                    t.t2.备注名,
                    t.t1.Money
                });

            // 对比数据差异
            foreach (var order in subMoneyOrderList)
            {
                if (!SubMoneyOrderIdList.Contains(order.Id))
                {
                    SubMoneyOrderIdList.Add(order.Id);
                    Invoke(() => { dataGridView_SubMoney.Rows.Add(order.Id, order.Account, order.备注名, order.Money, "同意", "拒绝"); });
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
        }
    }

    #endregion

    #region 显示平台日志

    private List<long> PlatformLogIdList { get; set; } = new();

    /// <summary>
    /// 显示平台日志
    /// </summary>
    private void ShowPlatformLogAsync()
    {
        Invoke(async () =>
        {
            try
            {
                // 查询Log表最后50条数据
                var logList = await DbHelper.FSql.Select<Log>()
                    .Where(t => t.Type == EnumLogType.平台)
                    .OrderByDescending(t => t.Id)
                    .Take(UiConstants.MaxLogRows)
                    .ToListAsync(a => new
                    {
                        a.Id,
                        时间 = Convert.ToDateTime(a.Time).ToString("HH:mm:ss"),
                        摘要 = a.Title,
                        详情 = a.Content
                    });

                // 没有数据直接返回
                if (!logList.Any())
                {
                    return;
                }

                // LogList倒序排列
                logList.Reverse();

                // 填充新数据
                bool needRefresh = false;
                foreach (var log in logList)
                {
                    if (!PlatformLogIdList.Contains(log.Id))
                    {
                        PlatformLogIdList.Add(log.Id);

                        try
                        {
                            dataGridView_PlatformLog.Rows.Add(log.Id, log.时间, log.摘要, log.详情);
                        }
                        catch (Exception ex)
                        {
                            // Debug.WriteLine(ex);
                            await DbHelper.AddLog(EnumLogType.机器人, "ShowPlatformLogAsync", ex.ToString());
                        }

                        needRefresh = true;
                    }
                }

                // 显示到最后一行
                if (needRefresh)
                {
                    try
                    {
                        while (dataGridView_PlatformLog.Rows.Count > UiConstants.MaxLogRows)
                        {
                            dataGridView_PlatformLog.Rows.RemoveAt(0);
                        }

                        // 取消选中第一行
                        dataGridView_PlatformLog.Rows[0].Selected = false;

                        // 显示最后一行
                        dataGridView_PlatformLog.FirstDisplayedScrollingRowIndex = dataGridView_PlatformLog.Rows.Count - 1;
                    }
                    catch (Exception ex)
                    {
                        // Debug.WriteLine(ex);
                        await DbHelper.AddLog(EnumLogType.机器人, "ShowPlatformLogAsync", ex.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "ShowSubMoneyOrderAsyncError", ex.ToString());
            }
        });
    }

    #endregion
}