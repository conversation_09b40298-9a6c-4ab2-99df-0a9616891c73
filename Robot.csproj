﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>Dynamo.ico</ApplicationIcon>
    <AssemblyVersion>1.0.7.9</AssemblyVersion>
    <FileVersion>1.0.7.9</FileVersion>
    <SatelliteResourceLanguages>xxx</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Dynamo.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autoupdater.NET.Official" Version="1.9.2" />
    <PackageReference Include="Costura.Fody" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="FreeSql" Version="3.5.209" />
    <PackageReference Include="FreeSql.DbContext" Version="3.5.209" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.209" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SunnyUI" Version="3.8.2" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Models\PostBetData.cs" />
    <Compile Remove="Helper\ImageUploader.cs" />
    <Compile Remove="Helper\ChatApiClient.cs" />
    <Compile Update="Ui\FormAlertMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormIndex.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormInputBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Ui\FormSetParentAccount.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AiHelper">
      <HintPath>D:\TreeDLL\AiHelper.dll</HintPath>
    </Reference>
    <Reference Include="ControlHelper">
      <HintPath>D:\TreeDLL\ControlHelper.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>